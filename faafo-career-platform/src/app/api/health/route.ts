import { NextRequest, NextResponse } from 'next/server';
import { log } from '@/lib/logger';
import prisma from '@/lib/prisma';
import { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

/**
 * Health Check API Endpoint
 * Provides system status and service availability
 */

interface HealthCheckResponse {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: string;
  version: string;
  uptime: number;
  services: {
    database: {
      status: 'up' | 'down';
      responseTime?: number;
      error?: string;
    };
    email: {
      status: 'configured' | 'not_configured';
      provider?: string;
    };
    ai: {
      status: 'configured' | 'not_configured';
      providers: string[];
    };
    cache: {
      status: 'configured' | 'not_configured';
      provider?: string;
    };
    errorTracking: {
      status: 'configured' | 'not_configured';
      provider?: string;
    };
  };
  environment: string;
  nodeVersion: string;
}

async function checkDatabaseHealth(): Promise<{
  status: 'up' | 'down';
  responseTime?: number;
  error?: string;
}> {
  try {
    const startTime = Date.now();
    
    // Simple database connectivity test
    await prisma.$queryRaw`SELECT 1 as health_check`;
    
    const responseTime = Date.now() - startTime;
    
    log.database('health_check', 'system', responseTime);
    
    return {
      status: 'up',
      responseTime
    };
  } catch (error) {
    log.error('Database health check failed', error as Error, {
      component: 'health_check'
    });
    
    return {
      status: 'down',
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

function checkEmailService(): {
  status: 'configured' | 'not_configured';
  provider?: string;
} {
  const resendApiKey = process.env.RESEND_API_KEY;
  const sendgridApiKey = process.env.SENDGRID_API_KEY;
  
  if (resendApiKey) {
    return { status: 'configured', provider: 'resend' };
  } else if (sendgridApiKey) {
    return { status: 'configured', provider: 'sendgrid' };
  } else {
    return { status: 'not_configured' };
  }
}

function checkAIServices(): {
  status: 'configured' | 'not_configured';
  providers: string[];
} {
  const providers: string[] = [];
  
  if (process.env.GOOGLE_GEMINI_API_KEY) {
    providers.push('google_gemini');
  }
  
  if (process.env.OPENAI_API_KEY) {
    providers.push('openai');
  }
  
  if (process.env.ANTHROPIC_API_KEY) {
    providers.push('anthropic');
  }
  
  return {
    status: providers.length > 0 ? 'configured' : 'not_configured',
    providers
  };
}

function checkCacheService(): {
  status: 'configured' | 'not_configured';
  provider?: string;
} {
  if (process.env.REDIS_URL) {
    return { status: 'configured', provider: 'redis' };
  } else {
    return { status: 'not_configured' };
  }
}

function checkErrorTracking(): {
  status: 'configured' | 'not_configured';
  provider?: string;
} {
  if (process.env.NEXT_PUBLIC_SENTRY_DSN) {
    return { status: 'configured', provider: 'sentry' };
  } else {
    return { status: 'not_configured' };
  }
}

export const GET = withUnifiedErrorHandling(async (request: NextRequest) => {
  const startTime = Date.now();

  log.info('Health check requested', {
    component: 'health_check',
    metadata: { userAgent: request.headers.get('user-agent') || 'unknown' }
  });

  // Check all services
  const [databaseHealth] = await Promise.all([
    checkDatabaseHealth()
  ]);

  const emailHealth = checkEmailService();
  const aiHealth = checkAIServices();
  const cacheHealth = checkCacheService();
  const errorTrackingHealth = checkErrorTracking();

  // Determine overall status
  let overallStatus: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';

  if (databaseHealth.status === 'down') {
    overallStatus = 'unhealthy';
  } else if (
    emailHealth.status === 'not_configured' ||
    aiHealth.status === 'not_configured'
  ) {
    overallStatus = 'degraded';
  }

  const response: HealthCheckResponse = {
    status: overallStatus,
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version || '1.0.0',
    uptime: process.uptime(),
    services: {
      database: databaseHealth,
      email: emailHealth,
      ai: aiHealth,
      cache: cacheHealth,
      errorTracking: errorTrackingHealth
    },
    environment: process.env.NODE_ENV || 'development',
    nodeVersion: process.version
  };

  const duration = Date.now() - startTime;

  log.api('GET', '/api/health', 200, duration, {
    component: 'health_check',
    metadata: { status: overallStatus }
  });

  // Return appropriate HTTP status based on health
  const httpStatus = overallStatus === 'healthy' ? 200 :
                    overallStatus === 'degraded' ? 200 : 503;

  // For unhealthy status, throw error to get proper status code
  if (overallStatus === 'unhealthy') {
    const error = new Error('Health check failed') as any;
    error.statusCode = 503;
    error.data = response;
    throw error;
  }

  return NextResponse.json({
    success: true,
    data: response
  }, { status: httpStatus });
});

// Simple ping endpoint for basic availability checks
export const HEAD = withUnifiedErrorHandling(async (request: NextRequest) => {
  // Quick database ping
  await prisma.$queryRaw`SELECT 1`;

  return new NextResponse(null, { status: 200 });
});
