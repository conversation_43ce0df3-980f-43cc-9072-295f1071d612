import { NextResponse } from 'next/server';
import { ZodError } from 'zod';
import { Prisma } from '@prisma/client';

export interface APIError {
  code: string;
  message: string;
  details?: unknown;
  statusCode: number;
}

export class AppError extends Error {
  public readonly code: string;
  public readonly statusCode: number;
  public readonly details?: unknown;

  constructor(code: string, message: string, statusCode: number = 500, details?: unknown) {
    super(message);
    this.code = code;
    this.statusCode = statusCode;
    this.details = details;
    this.name = 'AppError';
  }
}

// Predefined error codes
export const ErrorCodes = {
  // Authentication & Authorization
  UNAUTHORIZED: 'UNAUTHORIZED',
  FORBIDDEN: 'FORBIDDEN',
  INVALID_CREDENTIALS: 'INVALID_CREDENTIALS',
  TOKEN_EXPIRED: 'TOKEN_EXPIRED',
  ACCOUNT_LOCKED: 'ACCOUNT_LOCKED',
  
  // Validation
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  INVALID_INPUT: 'INVALID_INPUT',
  MISSING_REQUIRED_FIELD: 'MISSING_REQUIRED_FIELD',
  
  // Resources
  NOT_FOUND: 'NOT_FOUND',
  ALREADY_EXISTS: 'ALREADY_EXISTS',
  CONFLICT: 'CONFLICT',
  
  // Rate Limiting
  RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
  
  // Server Errors
  INTERNAL_ERROR: 'INTERNAL_ERROR',
  DATABASE_ERROR: 'DATABASE_ERROR',
  EXTERNAL_SERVICE_ERROR: 'EXTERNAL_SERVICE_ERROR',
  
  // Business Logic
  INSUFFICIENT_PERMISSIONS: 'INSUFFICIENT_PERMISSIONS',
  OPERATION_NOT_ALLOWED: 'OPERATION_NOT_ALLOWED',
  RESOURCE_LIMIT_EXCEEDED: 'RESOURCE_LIMIT_EXCEEDED',
} as const;

export function createError(
  code: keyof typeof ErrorCodes,
  message: string,
  statusCode: number = 500,
  details?: unknown
): AppError {
  return new AppError(ErrorCodes[code], message, statusCode, details);
}

export function handleAPIError(error: unknown): NextResponse {
  console.error('API Error:', error);

  // Handle custom AppError
  if (error instanceof AppError) {
    return NextResponse.json(
      {
        error: {
          code: error.code,
          message: error instanceof Error ? error.message : String(error),
          ...(process.env.NODE_ENV === 'development' && { details: error.details })
        }
      },
      { status: error.statusCode }
    );
  }

  // Handle Zod validation errors
  if (error instanceof ZodError) {
    const validationErrors = error.errors.map(err => ({
      field: err.path.join('.'),
      message: err.message
    }));

    return NextResponse.json(
      {
        error: {
          code: ErrorCodes.VALIDATION_ERROR,
          message: 'Validation failed',
          details: validationErrors
        }
      },
      { status: 400 }
    );
  }

  // Handle Prisma errors
  if (error instanceof Prisma.PrismaClientKnownRequestError) {
    switch (error.code) {
      case 'P2002':
        return NextResponse.json(
          {
            error: {
              code: ErrorCodes.ALREADY_EXISTS,
              message: 'A record with this information already exists',
              details: process.env.NODE_ENV === 'development' ? error.meta : undefined
            }
          },
          { status: 409 }
        );
      case 'P2025':
        return NextResponse.json(
          {
            error: {
              code: ErrorCodes.NOT_FOUND,
              message: 'The requested resource was not found'
            }
          },
          { status: 404 }
        );
      default:
        return NextResponse.json(
          {
            error: {
              code: ErrorCodes.DATABASE_ERROR,
              message: 'Database operation failed'
            }
          },
          { status: 500 }
        );
    }
  }

  // Handle generic errors
  if (error instanceof Error) {
    return NextResponse.json(
      {
        error: {
          code: ErrorCodes.INTERNAL_ERROR,
          message: process.env.NODE_ENV === 'development' ? (error instanceof Error ? error.message : String(error)) : 'Internal server error',
          ...(process.env.NODE_ENV === 'development' && { stack: error instanceof Error ? error.stack : String(error) })
        }
      },
      { status: 500 }
    );
  }

  // Handle unknown errors
  return NextResponse.json(
    {
      error: {
        code: ErrorCodes.INTERNAL_ERROR,
        message: 'An unexpected error occurred'
      }
    },
    { status: 500 }
  );
}

// Wrapper function for API route handlers
export function withErrorHandler(
  handler: (...args: any[]) => Promise<NextResponse>
) {
  return async (...args: any[]): Promise<NextResponse> => {
    try {
      return await handler(...args);
    } catch (error) {
      return handleAPIError(error);
    }
  };
}

// Success response helper
export function createSuccessResponse(
  data: unknown,
  message?: string,
  statusCode: number = 200
): NextResponse {
  return NextResponse.json(
    {
      success: true,
      ...(message && { message }),
      data
    },
    { status: statusCode }
  );
}

// Pagination helper
export interface PaginationParams {
  page: number;
  limit: number;
  total: number;
}

export function createPaginatedResponse(
  data: unknown[],
  pagination: PaginationParams,
  message?: string
): NextResponse {
  const { page, limit, total } = pagination;
  const totalPages = Math.ceil(total / limit);
  const hasNext = page < totalPages;
  const hasPrev = page > 1;

  return NextResponse.json(
    {
      success: true,
      ...(message && { message }),
      data,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext,
        hasPrev
      }
    },
    { status: 200 }
  );
}

// Validation helper
export function validateRequiredFields(
  data: Record<string, unknown>,
  requiredFields: string[]
): void {
  const missingFields = requiredFields.filter(field => 
    data[field] === undefined || data[field] === null || data[field] === ''
  );

  if (missingFields.length > 0) {
    throw createError(
      'MISSING_REQUIRED_FIELD',
      `Missing required fields: ${missingFields.join(', ')}`,
      400,
      { missingFields }
    );
  }
}

// Authorization helper
export function requireAuth(session: any): asserts session is { user: { id: string } } {
  if (!session || !session.user?.id) {
    throw createError('UNAUTHORIZED', 'Authentication required', 401);
  }
}

// Permission helper
export function requirePermission(
  userRole: string,
  requiredRole: string,
  resource?: string
): void {
  // Simple role-based check (extend as needed)
  const roleHierarchy = ['user', 'moderator', 'admin'];
  const userLevel = roleHierarchy.indexOf(userRole);
  const requiredLevel = roleHierarchy.indexOf(requiredRole);

  if (userLevel === -1 || userLevel < requiredLevel) {
    throw createError(
      'INSUFFICIENT_PERMISSIONS',
      `Insufficient permissions to access ${resource || 'this resource'}`,
      403
    );
  }
}
