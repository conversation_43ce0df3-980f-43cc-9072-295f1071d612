#!/usr/bin/env node

/**
 * Audit CLI Interface
 * 
 * Command-line interface for running codebase audits with
 * progress reporting and configurable options.
 */

import { Command } from 'commander';
import * as chalk from 'chalk';
import ora from 'ora';
import { CoreAuditEngine } from '../core-audit-engine';
import {
  AuditRunConfig,
  AuditProgress,
  IssueSeverity,
  IssueCategory,
  AuditStatus
} from '../types';
import { logger } from '../../logger';

class AuditCLI {
  private program: any;
  private spinner: any;

  constructor() {
    this.program = new Command();
    this.setupCommands();
  }

  private setupCommands(): void {
    this.program
      .name('audit')
      .description('FAAFO Codebase Audit System')
      .version('1.0.0');

    // Main audit command
    this.program
      .command('run')
      .description('Run a comprehensive codebase audit')
      .option('-c, --categories <categories>', 'Comma-separated list of categories to include')
      .option('-e, --exclude-categories <categories>', 'Comma-separated list of categories to exclude')
      .option('-p, --paths <paths>', 'Comma-separated list of paths to include')
      .option('-x, --exclude-paths <paths>', 'Comma-separated list of paths to exclude')
      .option('-s, --severity <level>', 'Minimum severity level (low, medium, high, critical)')
      .option('-m, --max-issues <number>', 'Maximum number of issues to report')
      .option('--no-security', 'Skip security analysis')
      .option('--no-performance', 'Skip performance analysis')
      .option('--no-ai', 'Skip AI-powered analysis')
      .option('-o, --output <format>', 'Output format (console, json, html)', 'console')
      .option('-f, --file <path>', 'Output file path')
      .option('--verbose', 'Enable verbose logging')
      .action(this.runAudit.bind(this));

    // List recent audits
    this.program
      .command('list')
      .description('List recent audit runs')
      .option('-l, --limit <number>', 'Number of audits to show', '10')
      .action(this.listAudits.bind(this));

    // Show audit details
    this.program
      .command('show <auditId>')
      .description('Show details of a specific audit run')
      .option('-f, --format <format>', 'Output format (console, json)', 'console')
      .action(this.showAudit.bind(this));

    // Clean up old audits
    this.program
      .command('cleanup')
      .description('Clean up old audit runs')
      .option('-d, --days <number>', 'Days to keep (default: 90)', '90')
      .action(this.cleanupAudits.bind(this));
  }

  private async runAudit(options: any): Promise<void> {
    try {
      console.log(chalk.blue.bold('🔍 FAAFO Codebase Audit System\n'));

      // Parse options
      const config = this.parseAuditConfig(options);
      
      if (options.verbose) {
        console.log(chalk.gray('Configuration:'));
        console.log(chalk.gray(JSON.stringify(config, null, 2)));
        console.log();
      }

      // Initialize audit engine
      const engine = new CoreAuditEngine(undefined, this.createProgressCallback());

      // Start audit
      this.spinner = ora('Initializing audit...').start();
      
      const result = await engine.runAudit(config);

      this.spinner.stop();

      // Output results
      await this.outputResults(result, options.output, options.file);

    } catch (error) {
      if (this.spinner) {
        this.spinner.fail('Audit failed');
      }
      console.error(chalk.red('Error:'), error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : String(error) : String(error) : error);
      process.exit(1);
    }
  }

  private parseAuditConfig(options: any): AuditRunConfig {
    const config: AuditRunConfig = {};

    if (options.categories) {
      config.includeCategories = options.categories.split(',').map((c: string) => 
        c.trim().toUpperCase() as IssueCategory
      );
    }

    if (options.excludeCategories) {
      config.excludeCategories = options.excludeCategories.split(',').map((c: string) => 
        c.trim().toUpperCase() as IssueCategory
      );
    }

    if (options.paths) {
      config.includePaths = options.paths.split(',').map((p: string) => p.trim());
    }

    if (options.excludePaths) {
      config.excludePaths = options.excludePaths.split(',').map((p: string) => p.trim());
    }

    if (options.severity) {
      config.severityThreshold = options.severity.toUpperCase() as IssueSeverity;
    }

    if (options.maxIssues) {
      config.maxIssues = parseInt(options.maxIssues, 10);
    }

    config.enableSecurityScan = !options.noSecurity;
    config.enablePerformanceAnalysis = !options.noPerformance;
    config.enableAIAnalysis = !options.noAi;

    return config;
  }

  private createProgressCallback(): (progress: AuditProgress) => void {
    return (progress: AuditProgress) => {
      if (this.spinner) {
        const percentage = Math.round(progress.progress);
        const currentFile = progress.currentFile ? ` (${progress.currentFile})` : '';
        this.spinner.text = `${progress.phase} - ${percentage}%${currentFile}`;
      }
    };
  }

  private async outputResults(result: any, format: string, filePath?: string): Promise<void> {
    switch (format) {
      case 'json':
        await this.outputJSON(result, filePath);
        break;
      case 'html':
        await this.outputHTML(result, filePath);
        break;
      default:
        this.outputConsole(result);
    }
  }

  private outputConsole(result: any): void {
    console.log(chalk.green.bold('✅ Audit Completed\n'));

    // Summary
    console.log(chalk.bold('Summary:'));
    console.log(`  Total Issues: ${chalk.yellow(result.totalIssues)}`);
    console.log(`  Critical: ${chalk.red(result.criticalCount)}`);
    console.log(`  High: ${chalk.red(result.highCount)}`);
    console.log(`  Medium: ${chalk.yellow(result.mediumCount)}`);
    console.log(`  Low: ${chalk.gray(result.lowCount)}`);
    console.log(`  Duration: ${this.formatDuration(result.startedAt, result.completedAt)}\n`);

    // Issues by category
    const issuesByCategory = this.groupIssuesByCategory(result.issues);
    
    for (const [category, issues] of Object.entries(issuesByCategory)) {
      if (issues.length > 0) {
        console.log(chalk.bold(`${category} (${issues.length} issues):`));
        
        // Show top 5 issues per category
        const topIssues = issues.slice(0, 5);
        for (const issue of topIssues) {
          const severityColor = this.getSeverityColor(issue.severity);
          console.log(`  ${severityColor(issue.severity.padEnd(8))} ${issue.title}`);
          console.log(`    ${chalk.gray(issue.filePath)}${issue.lineNumber ? `:${issue.lineNumber}` : ''}`);
        }
        
        if (issues.length > 5) {
          console.log(`    ${chalk.gray(`... and ${issues.length - 5} more`)}`);
        }
        console.log();
      }
    }

    // Recommendations
    if (result.totalIssues > 0) {
      console.log(chalk.bold('Next Steps:'));
      console.log('  1. Address critical and high severity issues first');
      console.log('  2. Review security vulnerabilities immediately');
      console.log('  3. Consider running with --verbose for detailed recommendations');
      console.log(`  4. View full report: audit show ${result.id}`);
    }
  }

  private async outputJSON(result: any, filePath?: string): Promise<void> {
    const output = JSON.stringify(result, null, 2);
    
    if (filePath) {
      const fs = await import('fs/promises');
      await fs.writeFile(filePath, output);
      console.log(chalk.green(`Results saved to ${filePath}`));
    } else {
      console.log(output);
    }
  }

  private async outputHTML(result: any, filePath?: string): Promise<void> {
    const html = this.generateHTMLReport(result);
    
    if (filePath) {
      const fs = await import('fs/promises');
      await fs.writeFile(filePath, html);
      console.log(chalk.green(`HTML report saved to ${filePath}`));
    } else {
      console.log(html);
    }
  }

  private generateHTMLReport(result: any): string {
    // Simplified HTML report generation
    return `
<!DOCTYPE html>
<html>
<head>
    <title>Audit Report - ${result.id}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .summary { background: #f5f5f5; padding: 20px; border-radius: 5px; }
        .issue { margin: 10px 0; padding: 10px; border-left: 4px solid #ccc; }
        .critical { border-left-color: #d32f2f; }
        .high { border-left-color: #f57c00; }
        .medium { border-left-color: #fbc02d; }
        .low { border-left-color: #388e3c; }
    </style>
</head>
<body>
    <h1>Codebase Audit Report</h1>
    <div class="summary">
        <h2>Summary</h2>
        <p>Total Issues: ${result.totalIssues}</p>
        <p>Critical: ${result.criticalCount} | High: ${result.highCount} | Medium: ${result.mediumCount} | Low: ${result.lowCount}</p>
        <p>Completed: ${new Date(result.completedAt).toLocaleString()}</p>
    </div>
    
    <h2>Issues</h2>
    ${result.issues.map((issue: any) => `
        <div class="issue ${issue.severity.toLowerCase()}">
            <h3>${issue.title}</h3>
            <p><strong>File:</strong> ${issue.filePath}${issue.lineNumber ? `:${issue.lineNumber}` : ''}</p>
            <p><strong>Severity:</strong> ${issue.severity}</p>
            <p><strong>Category:</strong> ${issue.category}</p>
            <p>${issue.description}</p>
            ${issue.recommendation ? `<p><strong>Recommendation:</strong> ${issue.recommendation}</p>` : ''}
        </div>
    `).join('')}
</body>
</html>`;
  }

  private async listAudits(options: any): Promise<void> {
    // Stub implementation - would list recent audits from database
    console.log(chalk.blue('Recent Audit Runs:'));
    console.log(chalk.gray('No audits found. Run "audit run" to create your first audit.'));
  }

  private async showAudit(auditId: string, options: any): Promise<void> {
    // Stub implementation - would show specific audit details
    console.log(chalk.blue(`Audit Details: ${auditId}`));
    console.log(chalk.gray('Audit not found or not implemented yet.'));
  }

  private async cleanupAudits(options: any): Promise<void> {
    // Stub implementation - would cleanup old audits
    const days = parseInt(options.days, 10);
    console.log(chalk.blue(`Cleaning up audits older than ${days} days...`));
    console.log(chalk.green('Cleanup completed.'));
  }

  private groupIssuesByCategory(issues: any[]): Record<string, any[]> {
    return issues.reduce((groups, issue) => {
      const category = issue.category;
      if (!groups[category]) {
        groups[category] = [];
      }
      groups[category].push(issue);
      return groups;
    }, {});
  }

  private getSeverityColor(severity: string): (text: string) => string {
    switch (severity.toUpperCase()) {
      case 'CRITICAL':
        return chalk.red.bold;
      case 'HIGH':
        return chalk.red;
      case 'MEDIUM':
        return chalk.yellow;
      case 'LOW':
        return chalk.gray;
      default:
        return chalk.white;
    }
  }

  private formatDuration(start: string, end: string): string {
    const duration = new Date(end).getTime() - new Date(start).getTime();
    const seconds = Math.round(duration / 1000);
    
    if (seconds < 60) {
      return `${seconds}s`;
    }
    
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  }

  public run(): void {
    this.program.parse();
  }
}

// Run CLI if this file is executed directly
if (require.main === module) {
  const cli = new AuditCLI();
  cli.run();
}

export { AuditCLI };
