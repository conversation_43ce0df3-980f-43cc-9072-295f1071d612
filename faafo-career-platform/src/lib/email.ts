import { Resend } from 'resend';
import { render } from '@react-email/render';
import React from 'react';
import { PasswordResetEmail } from '../emails/PasswordResetEmail';

const resend = process.env.RESEND_API_KEY ? new Resend(process.env.RESEND_API_KEY) : null;

interface EmailOptions {
  to: string;
  subject: string;
  template: React.ReactElement; // Use React.ReactElement for email templates
}

export async function sendEmail({ to, subject, template }: EmailOptions) {
  if (!resend) {
    console.warn('Email service not configured - RESEND_API_KEY missing');
    return { success: false, error: 'Email service not configured' };
  }

  try {
    const html = await render(template);

    const { data, error } = await resend.emails.send({
      from: process.env.EMAIL_FROM || '<EMAIL>', // Use environment variable or fallback
      to,
      subject,
      html,
    });

    if (error) {
      console.error('Error sending email:', error);
      return { success: false, error: error instanceof Error ? error.message : String(error) };
    }

    console.log('Email sent successfully:', data);
    return { success: true, data };
  } catch (error) {
    console.error('Caught an exception while sending email:', error);
    return { success: false, error: (error as Error).message };
  }
}

interface SendPasswordResetEmailParams {
  to: string;
  url: string;
}

export async function sendPasswordResetEmail(params: SendPasswordResetEmailParams) {
  if (!resend) {
    console.warn('Email service not configured - RESEND_API_KEY missing');
    return { success: false, error: 'Email service not configured' };
  }

  const { to, url } = params;
  try {
    await resend.emails.send({
      from: '<EMAIL>', // Use a verified sender from your Resend domain
      to,
      subject: 'Reset your password',
      react: React.createElement(PasswordResetEmail, { resetLink: url }),
    });
    return { success: true };
  } catch (error) {
    console.error('Error sending password reset email:', error);
    return { success: false, error: (error as Error).message };
  }
}