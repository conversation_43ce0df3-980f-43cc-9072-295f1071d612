/**
 * Comprehensive client-side validation for all forms
 * Prevents malicious content, validates input patterns, and provides user feedback
 */

import { z } from 'zod';
import { SecurityValidator } from './validation';

export interface ValidationResult {
  isValid: boolean;
  errors: Record<string, string>;
  warnings: string[];
  sanitizedData?: Record<string, any>;
}

export interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  customValidator?: (value: any) => string | null;
  sanitize?: boolean;
  allowHtml?: boolean;
}

export class ClientSideValidator {
  private static maliciousPatterns = [
    // XSS patterns
    /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
    /<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi,
    /javascript:/gi,
    /vbscript:/gi,
    /on\w+\s*=/gi,
    /eval\s*\(/gi,
    /expression\s*\(/gi,
    
    // SQL injection patterns
    /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)/gi,
    /(--|#|\/\*|\*\/)/g,
    /(\b(OR|AND)\b\s+\d+\s*=\s*\d+)/gi,
    
    // Command injection patterns
    /[;&|`$(){}[\]]/g,
    /\.\.\//g,
    
    // Template injection patterns
    /\{\{.*\}\}/g,
    /\$\{.*\}/g,
  ];

  /**
   * Validate a single field value
   */
  static validateField(
    fieldName: string,
    value: any,
    rules: ValidationRule
  ): { isValid: boolean; error?: string; warning?: string; sanitizedValue?: any } {
    const errors: string[] = [];
    const warnings: string[] = [];
    let sanitizedValue = value;

    // Type check
    if (typeof value !== 'string' && value !== null && value !== undefined) {
      value = String(value);
    }

    // Required validation
    if (rules.required && (!value || value.trim() === '')) {
      return { isValid: false, error: `${fieldName} is required` };
    }

    // Skip further validation if value is empty and not required
    if (!value || value.trim() === '') {
      return { isValid: true, sanitizedValue: '' };
    }

    const stringValue = String(value).trim();

    // Length validation
    if (rules.minLength && stringValue.length < rules.minLength) {
      errors.push(`${fieldName} must be at least ${rules.minLength} characters long`);
    }

    if (rules.maxLength && stringValue.length > rules.maxLength) {
      errors.push(`${fieldName} cannot exceed ${rules.maxLength} characters`);
    }

    // Pattern validation
    if (rules.pattern && !rules.pattern.test(stringValue)) {
      errors.push(`${fieldName} format is invalid`);
    }

    // Security validation
    const securityResult = SecurityValidator.validateSecurity(stringValue);
    if (!securityResult.isValid) {
      errors.push(`${fieldName} contains potentially harmful content: ${securityResult.threats.join(', ')}`);
    }

    // Malicious pattern detection
    const maliciousDetected = this.maliciousPatterns.some(pattern => pattern.test(stringValue));
    if (maliciousDetected) {
      errors.push(`${fieldName} contains potentially malicious content`);
    }

    // Sanitization
    if (rules.sanitize) {
      sanitizedValue = SecurityValidator.sanitizeInput(stringValue, {
        allowHtml: rules.allowHtml || false,
        maxLength: rules.maxLength,
        preserveNewlines: true
      });

      if (sanitizedValue !== stringValue) {
        warnings.push(`${fieldName} was automatically cleaned for security`);
      }
    }

    // Custom validation
    if (rules.customValidator) {
      const customError = rules.customValidator(sanitizedValue);
      if (customError) {
        errors.push(customError);
      }
    }

    return {
      isValid: errors.length === 0,
      error: errors.length > 0 ? errors[0] : undefined,
      warning: warnings.length > 0 ? warnings[0] : undefined,
      sanitizedValue
    };
  }

  /**
   * Validate an entire form
   */
  static validateForm(
    data: Record<string, any>,
    rules: Record<string, ValidationRule>
  ): ValidationResult {
    const errors: Record<string, string> = {};
    const warnings: string[] = [];
    const sanitizedData: Record<string, any> = {};

    for (const [fieldName, fieldRules] of Object.entries(rules)) {
      const fieldValue = data[fieldName];
      const result = this.validateField(fieldName, fieldValue, fieldRules);

      if (!result.isValid && result.error) {
        errors[fieldName] = result.error;
      }

      if (result.warning) {
        warnings.push(result.warning);
      }

      sanitizedData[fieldName] = result.sanitizedValue;
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors,
      warnings,
      sanitizedData
    };
  }

  /**
   * Real-time field validation for immediate feedback
   */
  static validateFieldRealTime(
    fieldName: string,
    value: any,
    rules: ValidationRule,
    debounceMs: number = 300
  ): Promise<{ isValid: boolean; error?: string; warning?: string }> {
    return new Promise((resolve) => {
      setTimeout(() => {
        const result = this.validateField(fieldName, value, rules);
        resolve({
          isValid: result.isValid,
          error: result.error,
          warning: result.warning
        });
      }, debounceMs);
    });
  }
}

/**
 * Pre-configured validation rules for common form fields
 */
export const CommonValidationRules = {
  email: {
    required: true,
    maxLength: 254,
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    sanitize: true,
    customValidator: (value: string) => {
      if (value && !value.includes('@')) {
        return 'Please enter a valid email address';
      }
      return null;
    }
  },

  password: {
    required: true,
    minLength: 8,
    maxLength: 128,
    pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
    sanitize: false, // Never sanitize passwords
    customValidator: (value: string) => {
      if (value && !/(?=.*[a-z])/.test(value)) {
        return 'Password must contain at least one lowercase letter';
      }
      if (value && !/(?=.*[A-Z])/.test(value)) {
        return 'Password must contain at least one uppercase letter';
      }
      if (value && !/(?=.*\d)/.test(value)) {
        return 'Password must contain at least one number';
      }
      if (value && !/(?=.*[@$!%*?&])/.test(value)) {
        return 'Password must contain at least one special character';
      }
      return null;
    }
  },

  name: {
    required: true,
    minLength: 1,
    maxLength: 50,
    pattern: /^[a-zA-Z\s'-]+$/,
    sanitize: true,
    customValidator: (value: string) => {
      if (value && /^\s+$/.test(value)) {
        return 'Name cannot be only whitespace';
      }
      return null;
    }
  },

  phone: {
    required: false,
    maxLength: 20,
    pattern: /^[\+]?[1-9][\d\s\-\(\)]{7,18}$/,
    sanitize: true,
    customValidator: (value: string) => {
      if (value && value.replace(/[\s\-\(\)]/g, '').length < 8) {
        return 'Phone number is too short';
      }
      return null;
    }
  },

  url: {
    required: false,
    maxLength: 2048,
    pattern: /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/,
    sanitize: true,
    customValidator: (value: string) => {
      if (value && !value.startsWith('http')) {
        return 'URL must start with http:// or https://';
      }
      return null;
    }
  },

  shortText: {
    required: false,
    maxLength: 100,
    sanitize: true,
    allowHtml: false
  },

  longText: {
    required: false,
    maxLength: 2000,
    sanitize: true,
    allowHtml: false
  },

  bio: {
    required: false,
    maxLength: 500,
    sanitize: true,
    allowHtml: false
  }
};

/**
 * Zod schemas for form validation
 */
export const signupSchema = z.object({
  email: z.string()
    .email('Please enter a valid email address')
    .max(254, 'Email address is too long'),
  password: z.string()
    .min(8, 'Password must be at least 8 characters long')
    .max(128, 'Password is too long')
    .regex(/(?=.*[a-z])/, 'Password must contain at least one lowercase letter')
    .regex(/(?=.*[A-Z])/, 'Password must contain at least one uppercase letter')
    .regex(/(?=.*\d)/, 'Password must contain at least one number')
    .regex(/(?=.*[@$!%*?&])/, 'Password must contain at least one special character')
});

export const loginSchema = z.object({
  email: z.string()
    .email('Please enter a valid email address')
    .max(254, 'Email address is too long'),
  password: z.string()
    .min(1, 'Password is required')
    .max(128, 'Password is too long')
});

/**
 * Form-specific validation rule sets
 */
export const FormValidationRules = {
  login: {
    email: CommonValidationRules.email,
    password: {
      required: true,
      minLength: 1,
      maxLength: 128,
      sanitize: false // Never sanitize passwords
      // No pattern or customValidator for login - just check if password exists
    }
  },

  signup: {
    email: CommonValidationRules.email,
    password: CommonValidationRules.password
  },

  profile: {
    firstName: CommonValidationRules.name,
    lastName: CommonValidationRules.name,
    bio: CommonValidationRules.bio,
    location: CommonValidationRules.shortText,
    phoneNumber: CommonValidationRules.phone,
    website: CommonValidationRules.url
  },

  contact: {
    name: CommonValidationRules.name,
    email: CommonValidationRules.email,
    subject: { ...CommonValidationRules.shortText, required: true, maxLength: 200 },
    message: { ...CommonValidationRules.longText, required: true, minLength: 10 }
  },

  resume: {
    title: { ...CommonValidationRules.shortText, required: true },
    summary: CommonValidationRules.longText,
    company: CommonValidationRules.shortText,
    position: CommonValidationRules.shortText,
    description: CommonValidationRules.longText
  },

  interviewPractice: {
    responseText: {
      required: true,
      minLength: 10,
      maxLength: 5000,
      sanitize: true,
      allowHtml: false
    },
    userNotes: {
      required: false,
      maxLength: 1000,
      sanitize: true,
      allowHtml: false
    },
    careerPath: CommonValidationRules.shortText,
    specificRole: CommonValidationRules.shortText
  }
};
