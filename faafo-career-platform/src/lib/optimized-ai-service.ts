/**
 * Optimized AI Service with Advanced Request Deduplication
 * 
 * Integrates advanced deduplication, intelligent caching, and performance monitoring
 * for optimal AI service call efficiency in Phase 2 implementation.
 */

import { geminiService } from './services/geminiService';
import { consolidatedCache } from './services/consolidated-cache-service';
import { enhancedCacheService } from './services/enhanced-cache-service';
import { AIServiceLogger } from './services/geminiService';

interface OptimizedAIConfig {
  enableAdvancedDeduplication: boolean;
  enableIntelligentCaching: boolean;
  enablePerformanceMonitoring: boolean;
  enableRequestBatching: boolean;
  maxConcurrentRequests: number;
  defaultTimeout: number;
  enableFallbacks: boolean;
}

interface AIRequestOptions {
  userId?: string;
  priority?: 'low' | 'medium' | 'high';
  timeout?: number;
  enableDeduplication?: boolean;
  enableCaching?: boolean;
  cacheKey?: string;
  cacheTTL?: number;
  enableSemanticMatch?: boolean;
  enableCrossUserMatch?: boolean;
}

interface OptimizedAIResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  metadata: {
    source: 'cache' | 'deduplication' | 'ai' | 'fallback';
    responseTime: number;
    deduplicationSavings?: number;
    cacheHit?: boolean;
    requestId: string;
    timestamp: string;
  };
}

export class OptimizedAIService {
  private config: OptimizedAIConfig;
  private activeRequests: Map<string, Promise<any>>;
  private requestCounter: number;

  constructor(config?: Partial<OptimizedAIConfig>) {
    this.config = {
      enableAdvancedDeduplication: true,
      enableIntelligentCaching: true,
      enablePerformanceMonitoring: true,
      enableRequestBatching: true,
      maxConcurrentRequests: 10,
      defaultTimeout: 30000,
      enableFallbacks: true,
      ...config
    };

    this.activeRequests = new Map();
    this.requestCounter = 0;

    AIServiceLogger.info('Optimized AI Service initialized', {
      config: this.config
    });
  }

  /**
   * Optimized skills analysis with advanced deduplication
   */
  async analyzeSkillsGap(
    currentSkills: any[],
    targetCareerPath: string,
    experienceLevel: string,
    options: AIRequestOptions = {}
  ): Promise<OptimizedAIResponse<any>> {
    const requestId = this.generateRequestId();
    const startTime = Date.now();

    try {
      // Generate request key for deduplication
      const requestKey = `skills-analysis:${targetCareerPath}:${experienceLevel}:${JSON.stringify(currentSkills)}`;
      
      // Check cache first if enabled
      if (this.config.enableIntelligentCaching && options.enableCaching !== false) {
        const cacheKey = options.cacheKey || `skills_gap_${targetCareerPath}_${experienceLevel}`;
        const cached = await enhancedCacheService.get(cacheKey);
        if (cached) {
          return {
            success: true,
            data: cached,
            metadata: {
              source: 'cache',
              responseTime: Date.now() - startTime,
              cacheHit: true,
              requestId,
              timestamp: new Date().toISOString()
            }
          };
        }
      }

      // Use cache-based deduplication if enabled
      if (this.config.enableAdvancedDeduplication && options.enableDeduplication !== false) {
        // Check cache first
        const cachedResult = await consolidatedCache.get<any>(requestKey);
        if (cachedResult) {
          return {
            success: true,
            data: cachedResult,
            metadata: {
              source: 'cache',
              responseTime: 0,
              cacheHit: true,
              requestId: this.generateRequestId(),
              timestamp: new Date().toISOString()
            }
          };
        }

        // Execute request and cache result
        const startTime = Date.now();
        const result = await geminiService.analyzeSkillsGap(currentSkills, targetCareerPath, experienceLevel, options.userId);
        await consolidatedCache.set(requestKey, result, { ttl: 3600000, tags: ['skills_analysis', options.userId || 'anonymous'] });
        return {
          success: true,
          data: result,
          metadata: {
            source: 'ai',
            responseTime: Date.now() - startTime,
            cacheHit: false,
            requestId: this.generateRequestId(),
            timestamp: new Date().toISOString()
          }
        };
      }

      // Fallback to direct AI service call
      const result = await geminiService.analyzeSkillsGap(
        currentSkills, 
        targetCareerPath, 
        experienceLevel, 
        options.userId
      );

      return {
        success: result.success,
        data: result.data,
        error: result.error,
        metadata: {
          source: 'ai',
          responseTime: Date.now() - startTime,
          requestId,
          timestamp: new Date().toISOString()
        }
      };

    } catch (error) {
      AIServiceLogger.error('Optimized skills analysis failed', {
        requestId,
        error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : String(error) : String(error) : String(error) : String(error) : String(error)
      });

      return {
        success: false,
        error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : String(error) : String(error) : String(error) : String(error) : 'Unknown error',
        metadata: {
          source: 'ai',
          responseTime: Date.now() - startTime,
          requestId,
          timestamp: new Date().toISOString()
        }
      };
    }
  }

  /**
   * Optimized career recommendations with deduplication
   */
  async generateCareerRecommendations(
    skills: string[],
    preferences: any,
    options: AIRequestOptions = {}
  ): Promise<OptimizedAIResponse<any>> {
    const requestId = this.generateRequestId();
    const startTime = Date.now();

    try {
      const requestKey = `career-recommendations:${JSON.stringify(skills)}:${JSON.stringify(preferences)}`;
      
      // Check cache first
      if (this.config.enableIntelligentCaching && options.enableCaching !== false) {
        const cacheKey = options.cacheKey || `career_rec_${this.hashObject({ skills, preferences })}`;
        const cached = await enhancedCacheService.get(cacheKey);
        if (cached) {
          return {
            success: true,
            data: cached,
            metadata: {
              source: 'cache',
              responseTime: Date.now() - startTime,
              cacheHit: true,
              requestId,
              timestamp: new Date().toISOString()
            }
          };
        }
      }

      // Use cache-based deduplication
      if (this.config.enableAdvancedDeduplication && options.enableDeduplication !== false) {
        // Check cache first
        const cachedResult = await consolidatedCache.get<any>(requestKey);
        if (cachedResult) {
          return {
            success: true,
            data: cachedResult,
            metadata: {
              source: 'cache',
              responseTime: 0,
              cacheHit: true,
              requestId: this.generateRequestId(),
              timestamp: new Date().toISOString()
            }
          };
        }

        // Execute request and cache result
        const startTime = Date.now();
        const result = await geminiService.generateCareerRecommendations(skills, preferences, options.userId);
        await consolidatedCache.set(requestKey, result, { ttl: 3600000, tags: ['career_recommendations', options.userId || 'anonymous'] });
        return {
          success: true,
          data: result,
          metadata: {
            source: 'ai',
            responseTime: Date.now() - startTime,
            cacheHit: false,
            requestId: this.generateRequestId(),
            timestamp: new Date().toISOString()
          }
        };
      }

      // Direct AI service call
      const result = await geminiService.generateCareerRecommendations(skills, preferences, options.userId);

      return {
        success: result.success,
        data: result.data,
        error: result.error,
        metadata: {
          source: 'ai',
          responseTime: Date.now() - startTime,
          requestId,
          timestamp: new Date().toISOString()
        }
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : String(error) : String(error) : String(error) : String(error) : 'Unknown error',
        metadata: {
          source: 'ai',
          responseTime: Date.now() - startTime,
          requestId,
          timestamp: new Date().toISOString()
        }
      };
    }
  }

  /**
   * Optimized interview question generation
   */
  async generateInterviewQuestions(
    sessionConfig: any,
    options: AIRequestOptions = {}
  ): Promise<OptimizedAIResponse<any>> {
    const requestId = this.generateRequestId();
    const startTime = Date.now();

    try {
      const requestKey = `interview-questions:${JSON.stringify(sessionConfig)}`;
      
      // Use cache-based deduplication for interview questions
      if (this.config.enableAdvancedDeduplication && options.enableDeduplication !== false) {
        // Check cache first
        const cachedResult = await consolidatedCache.get<any>(requestKey);
        if (cachedResult) {
          return {
            success: true,
            data: cachedResult,
            metadata: {
              source: 'cache',
              responseTime: 0,
              cacheHit: true,
              requestId: this.generateRequestId(),
              timestamp: new Date().toISOString()
            }
          };
        }

        // Execute request and cache result
        const startTime = Date.now();
        const result = await geminiService.generateInterviewQuestions(sessionConfig);
        await consolidatedCache.set(requestKey, result, { ttl: 3600000, tags: ['interview_questions', options.userId || 'anonymous'] });
        return {
          success: true,
          data: result,
          metadata: {
            source: 'ai',
            responseTime: Date.now() - startTime,
            cacheHit: false,
            requestId: this.generateRequestId(),
            timestamp: new Date().toISOString()
          }
        };
      }

      // Direct AI service call
      const result = await geminiService.generateInterviewQuestions(sessionConfig);

      return {
        success: result.success,
        data: result.data,
        error: result.error,
        metadata: {
          source: 'ai',
          responseTime: Date.now() - startTime,
          requestId,
          timestamp: new Date().toISOString()
        }
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : String(error) : String(error) : String(error) : String(error) : 'Unknown error',
        metadata: {
          source: 'ai',
          responseTime: Date.now() - startTime,
          requestId,
          timestamp: new Date().toISOString()
        }
      };
    }
  }

  /**
   * Get optimization metrics and performance statistics
   */
  getOptimizationMetrics() {
    const deduplicationMetrics = consolidatedCache.getMetrics();
    
    return {
      deduplication: deduplicationMetrics,
      activeRequests: this.activeRequests.size,
      totalRequests: this.requestCounter,
      config: this.config
    };
  }

  private generateRequestId(): string {
    return `opt_ai_${++this.requestCounter}_${Date.now()}`;
  }

  private mapPriority(priority?: 'low' | 'medium' | 'high'): number {
    switch (priority) {
      case 'high': return 3;
      case 'medium': return 2;
      case 'low': return 1;
      default: return 2;
    }
  }

  private hashObject(obj: any): string {
    return Buffer.from(JSON.stringify(obj)).toString('base64').substring(0, 16);
  }
}

// Export singleton instance
export const optimizedAIService = new OptimizedAIService();
