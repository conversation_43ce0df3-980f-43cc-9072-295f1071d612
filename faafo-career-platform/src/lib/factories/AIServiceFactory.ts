/**
 * AI Service Factory
 * 
 * Factory for creating AI service instances with dependency injection.
 * Reduces coupling by providing a centralized way to create and configure
 * AI services with different providers.
 */

import {
  IAIService,
  ICacheService,
  ILogger,
  IInputValidator,
  IResponseParser,
  IAIServiceFactory,
  AIConfig,
  CacheConfig,
  ValidationConfig,
  AIResponse,
  AIOptions,
  SkillGapParams,
  CareerParams,
  InterviewParams,
  ResumeAnalysisParams,
  HealthStatus,
  CacheOptions,
  CacheMetrics,
  LogContext,
  ValidationResult,
  SecurityScanResult,
  AI_CONSTANTS,
  createAIOptions,
  createCacheOptions
} from '@/lib/interfaces/ai';



import { GoogleGenerativeAI, GenerativeModel } from '@google/generative-ai';

// Abstract Base Classes

abstract class AbstractAIService implements IAIService {
  constructor(
    protected cache: ICacheService,
    protected validator: IInputValidator,
    protected logger: ILogger,
    protected config: AIConfig
  ) {}

  abstract generateContent(prompt: string, options?: AIOptions): Promise<AIResponse>;
  abstract initialize(): Promise<void>;

  async analyzeSkillsGap(params: SkillGapParams): Promise<AIResponse> {
    const cacheKey = `skills-gap:${JSON.stringify(params)}`;
    
    return this.cache.deduplicate(cacheKey, async () => {
      const prompt = this.buildSkillsGapPrompt(params);
      return this.generateContent(prompt, { userId: params.userId });
    }, { ttl: this.config.cacheTTL });
  }

  async generateCareerRecommendations(params: CareerParams): Promise<AIResponse> {
    const cacheKey = `career-rec:${JSON.stringify(params)}`;
    
    return this.cache.deduplicate(cacheKey, async () => {
      const prompt = this.buildCareerRecommendationsPrompt(params);
      return this.generateContent(prompt, { userId: params.userId });
    }, { ttl: this.config.cacheTTL });
  }

  async generateInterviewQuestions(params: InterviewParams): Promise<AIResponse> {
    const cacheKey = `interview:${JSON.stringify(params)}`;
    
    return this.cache.deduplicate(cacheKey, async () => {
      const prompt = this.buildInterviewQuestionsPrompt(params);
      return this.generateContent(prompt, { userId: params.userId });
    }, { ttl: this.config.cacheTTL });
  }

  async analyzeResume(params: ResumeAnalysisParams): Promise<AIResponse> {
    const cacheKey = `resume:${JSON.stringify(params)}`;
    
    return this.cache.deduplicate(cacheKey, async () => {
      const prompt = this.buildResumeAnalysisPrompt(params);
      return this.generateContent(prompt, { userId: params.userId });
    }, { ttl: this.config.cacheTTL });
  }

  async healthCheck(): Promise<HealthStatus> {
    try {
      const startTime = Date.now();
      await this.generateContent('Health check', { timeout: 5000 });
      const responseTime = Date.now() - startTime;
      
      const cacheHealth = await this.cache.get('health-check');
      
      return {
        ai: true,
        cache: {
          redis: true, // Simplified - would check actual Redis connection
          memory: true
        },
        responseTime,
        lastCheck: new Date()
      };
    } catch (error) {
      this.logger.error('Health check failed', error);
      return {
        ai: false,
        cache: {
          redis: false,
          memory: true
        },
        lastCheck: new Date()
      };
    }
  }

  protected buildSkillsGapPrompt(params: SkillGapParams): string {
    return `Analyze the skills gap for career transition:
Current Skills: ${params.currentSkills.join(', ')}
Target Career: ${params.targetCareerPath}
Experience Level: ${params.experienceLevel}

Provide a detailed analysis of missing skills and learning recommendations.`;
  }

  protected buildCareerRecommendationsPrompt(params: CareerParams): string {
    return `Generate career recommendations based on:
Assessment Data: ${JSON.stringify(params.assessmentData)}
Current Skills: ${params.skills.join(', ')}
Preferences: ${JSON.stringify(params.preferences)}

Provide 3-5 specific career path recommendations with reasoning.`;
  }

  protected buildInterviewQuestionsPrompt(params: InterviewParams): string {
    return `Generate ${params.count || 5} interview questions for:
Career Path: ${params.careerPath}
Experience Level: ${params.experienceLevel}
Question Type: ${params.questionType}

Provide realistic, relevant interview questions with expected answer guidelines.`;
  }

  protected buildResumeAnalysisPrompt(params: ResumeAnalysisParams): string {
    return `Analyze this resume:
${params.resumeText}

${params.targetRole ? `Target Role: ${params.targetRole}` : ''}

Provide feedback on strengths, weaknesses, and improvement suggestions.`;
  }
}

// Concrete Implementations

class GeminiAIService extends AbstractAIService {
  private model: GenerativeModel | null = null;
  private genAI: GoogleGenerativeAI | null = null;

  async initialize(): Promise<void> {
    if (!this.config.apiKey) {
      throw new Error('Gemini API key not configured');
    }

    this.genAI = new GoogleGenerativeAI(this.config.apiKey);
    this.model = this.genAI.getGenerativeModel({ model: this.config.model });
    
    this.logger.info('Gemini AI service initialized', { model: this.config.model });
  }

  async generateContent(prompt: string, options?: AIOptions): Promise<AIResponse> {
    const opts = createAIOptions(options);
    const startTime = Date.now();

    try {
      // Validate input
      const validation = await this.validator.validatePrompt(prompt);
      if (!validation.isValid) {
        return {
          success: false,
          error: `Validation failed: ${validation.errors?.join(', ')}`
        };
      }

      if (!this.model) {
        await this.initialize();
      }

      const result = await this.model!.generateContent(prompt);
      const response = await result.response;
      const text = response.text();

      const responseTime = Date.now() - startTime;

      this.logger.info('AI content generated', {
        userId: opts.userId,
        responseTime,
        model: this.config.model
      });

      return {
        success: true,
        data: text,
        metadata: {
          responseTime,
          model: this.config.model,
          retryCount: 0
        }
      };
    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      this.logger.error('AI content generation failed', error, {
        userId: opts.userId,
        responseTime,
        prompt: prompt.substring(0, 100)
      });

      return {
        success: false,
        error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : String(error) : String(error) : String(error) : 'Unknown error',
        metadata: {
          responseTime,
          model: this.config.model,
          retryCount: 0
        }
      };
    }
  }
}

class MemoryCacheService implements ICacheService {
  private cache = new Map<string, { value: any; expires: number; tags: string[] }>();
  private metrics: CacheMetrics = {
    hits: 0,
    misses: 0,
    hitRate: 0,
    totalRequests: 0,
    averageResponseTime: 0,
    memoryUsage: 0
  };

  async get<T>(key: string): Promise<T | null> {
    this.metrics.totalRequests++;
    
    const entry = this.cache.get(key);
    if (!entry || Date.now() > entry.expires) {
      this.metrics.misses++;
      this.cache.delete(key);
      this.updateHitRate();
      return null;
    }

    this.metrics.hits++;
    this.updateHitRate();
    return entry.value;
  }

  async set<T>(key: string, value: T, options?: CacheOptions): Promise<void> {
    const opts = createCacheOptions(options);
    const expires = Date.now() + (opts.ttl || AI_CONSTANTS.DEFAULT_CACHE_TTL);
    
    this.cache.set(key, {
      value,
      expires,
      tags: opts.tags || []
    });
  }

  async delete(key: string): Promise<void> {
    this.cache.delete(key);
  }

  async clear(): Promise<void> {
    this.cache.clear();
    this.metrics = {
      hits: 0,
      misses: 0,
      hitRate: 0,
      totalRequests: 0,
      averageResponseTime: 0,
      memoryUsage: 0
    };
  }

  async invalidateByTags(tags: string[]): Promise<number> {
    let count = 0;
    this.cache.forEach((entry, key) => {
      if (entry.tags.some(tag => tags.includes(tag))) {
        this.cache.delete(key);
        count++;
      }
    });
    return count;
  }

  getMetrics(): CacheMetrics {
    return { ...this.metrics };
  }

  async deduplicate<T>(
    key: string,
    requestFunction: () => Promise<T>,
    options?: CacheOptions
  ): Promise<T> {
    // Check cache first
    const cached = await this.get<T>(key);
    if (cached !== null) {
      return cached;
    }

    // Execute function and cache result
    const result = await requestFunction();
    await this.set(key, result, options);
    return result;
  }

  private updateHitRate(): void {
    this.metrics.hitRate = this.metrics.totalRequests > 0 
      ? this.metrics.hits / this.metrics.totalRequests 
      : 0;
  }
}

class ConsoleLogger implements ILogger {
  info(message: string, context?: LogContext): void {
    console.log(`[AI-INFO] ${message}`, context || '');
  }

  warn(message: string, context?: LogContext): void {
    console.warn(`[AI-WARN] ${message}`, context || '');
  }

  error(message: string, error?: any, context?: LogContext): void {
    console.error(`[AI-ERROR] ${message}`, {
      error: error instanceof Error ? {
        message: error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : String(error) : String(error) : String(error),
        stack: error instanceof Error ? error instanceof Error ? error.stack : String(error) : String(error)
      } : error,
      context
    });
  }

  debug(message: string, context?: LogContext): void {
    if (process.env.NODE_ENV === 'development') {
      console.log(`[AI-DEBUG] ${message}`, context || '');
    }
  }
}

class SimpleInputValidator implements IInputValidator {
  validatePrompt(prompt: string): ValidationResult {
    const errors: string[] = [];

    if (!prompt || typeof prompt !== 'string') {
      errors.push('Prompt must be a non-empty string');
    }

    if (prompt.length > AI_CONSTANTS.MAX_PROMPT_LENGTH) {
      errors.push(`Prompt exceeds maximum length of ${AI_CONSTANTS.MAX_PROMPT_LENGTH}`);
    }

    return {
      isValid: errors.length === 0,
      errors: errors.length > 0 ? errors : undefined,
      sanitizedInput: this.sanitizeInput(prompt)
    };
  }

  validateSkillsData(skills: any): ValidationResult {
    if (!Array.isArray(skills)) {
      return { isValid: false, errors: ['Skills must be an array'] };
    }

    return { isValid: true, sanitizedInput: skills };
  }

  validateAssessmentData(data: any): ValidationResult {
    if (!data || typeof data !== 'object') {
      return { isValid: false, errors: ['Assessment data must be an object'] };
    }

    return { isValid: true, sanitizedInput: data };
  }

  sanitizeInput(input: any): any {
    if (typeof input === 'string') {
      return input.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
    }
    return input;
  }

  securityScan(input: string): SecurityScanResult {
    const threats: string[] = [];
    
    if (/<script/gi.test(input)) {
      threats.push('Script injection detected');
    }

    return {
      isSafe: threats.length === 0,
      riskLevel: threats.length > 0 ? 'high' : 'low',
      threats,
      blockedPatterns: [],
      blocked: threats.length > 0
    };
  }
}

class SimpleResponseParser implements IResponseParser {
  parseJSON<T>(response: string): T | null {
    try {
      return JSON.parse(response);
    } catch {
      return null;
    }
  }

  validateResponse(response: any, schema?: any): boolean {
    return response !== null && response !== undefined;
  }

  extractContent(response: any): string {
    if (typeof response === 'string') return response;
    if (response?.text) return response.text;
    if (response?.content) return response.content;
    return JSON.stringify(response);
  }

  handleParsingError(error: Error, response: string): any {
    console.error('Response parsing error:', error);
    return { error: 'Failed to parse response', raw: response };
  }
}

// Factory Implementation

export class AIServiceFactory implements IAIServiceFactory {
  createAIService(provider: string, config: AIConfig): IAIService {
    const cache = this.createCacheService({
      provider: 'memory',
      maxMemorySize: 100,
      defaultTTL: config.cacheTTL,
      enableCompression: false,
      compressionThreshold: 1024,
      enableMetrics: true
    });
    
    const validator = this.createValidator({
      maxPromptLength: AI_CONSTANTS.MAX_PROMPT_LENGTH,
      maxResponseLength: AI_CONSTANTS.MAX_RESPONSE_LENGTH,
      enableSecurityScan: true,
      blockedPatterns: [],
      allowedContentTypes: ['text/plain', 'application/json']
    });
    
    const logger = this.createLogger();

    switch (provider.toLowerCase()) {
      case 'gemini':
        return new GeminiAIService(cache, validator, logger, config);
      default:
        throw new Error(`Unsupported AI provider: ${provider}`);
    }
  }

  createCacheService(config: CacheConfig): ICacheService {
    // For now, only memory cache implementation
    return new MemoryCacheService();
  }

  createValidator(config: ValidationConfig): IInputValidator {
    return new SimpleInputValidator();
  }

  createResponseParser(): IResponseParser {
    return new SimpleResponseParser();
  }

  createLogger(): ILogger {
    return new ConsoleLogger();
  }
}

// Export singleton factory
export const aiServiceFactory = new AIServiceFactory();
