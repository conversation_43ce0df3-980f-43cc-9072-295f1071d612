/**
 * Decoupled Gemini Service
 * 
 * Refactored Gemini AI service using dependency injection to reduce coupling.
 * Uses interfaces and factory pattern for better testability and maintainability.
 */

import { GoogleGenerativeAI, GenerativeModel, GenerationConfig } from '@google/generative-ai';
import {
  IAIService,
  ICacheService,
  ILogger,
  IInputValidator,
  AIResponse,
  AIOptions,
  SkillGapParams,
  CareerParams,
  InterviewParams,
  ResumeAnalysisParams,
  HealthStatus,
  AIConfig,
  createAIOptions,
  AI_CONSTANTS
} from '@/lib/interfaces/ai';
import { aiServiceFactory } from '@/lib/factories/AIServiceFactory';

/**
 * Configuration for Gemini Service
 */
export interface GeminiServiceConfig extends AIConfig {
  model: string;
  generationConfig?: GenerationConfig;
  safetySettings?: any[];
}

/**
 * Decoupled Gemini Service Implementation
 * 
 * Uses dependency injection for all external dependencies.
 * Implements the IAIService interface for consistency.
 */
export class DecoupledGeminiService implements IAIService {
  private model: GenerativeModel | null = null;
  private genAI: GoogleGenerativeAI | null = null;
  private initialized = false;

  constructor(
    private config: GeminiServiceConfig,
    private cache: ICacheService,
    private validator: IInputValidator,
    private logger: ILogger
  ) {
    this.validateConfig();
  }

  /**
   * Initialize the Gemini AI service
   */
  async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      this.genAI = new GoogleGenerativeAI(this.config.apiKey);
      this.model = this.genAI.getGenerativeModel({
        model: this.config.model,
        generationConfig: this.config.generationConfig,
        safetySettings: this.config.safetySettings
      });

      this.initialized = true;
      this.logger.info('Gemini service initialized successfully', {
        model: this.config.model,
        provider: this.config.provider
      });
    } catch (error) {
      this.logger.error('Failed to initialize Gemini service', error);
      throw new Error(`Gemini service initialization failed: ${error}`);
    }
  }

  /**
   * Generate content using Gemini AI
   */
  async generateContent(prompt: string, options?: AIOptions): Promise<AIResponse> {
    const opts = createAIOptions(options);
    const startTime = Date.now();

    try {
      // Validate input
      const validation = await this.validator.validatePrompt(prompt);
      if (!validation.isValid) {
        return {
          success: false,
          error: `Input validation failed: ${validation.errors?.join(', ')}`,
          metadata: {
            responseTime: Date.now() - startTime,
            model: this.config.model,
            retryCount: 0
          }
        };
      }

      // Check cache first if caching is enabled
      if (this.config.enableCaching && opts.cacheKey) {
        const cached = await this.cache.get<string>(opts.cacheKey);
        if (cached) {
          this.logger.debug('Cache hit for AI request', {
            cacheKey: opts.cacheKey,
            userId: opts.userId
          });
          
          return {
            success: true,
            data: cached,
            cached: true,
            metadata: {
              responseTime: Date.now() - startTime,
              model: this.config.model,
              cacheHit: true,
              retryCount: 0
            }
          };
        }
      }

      // Ensure service is initialized
      if (!this.initialized) {
        await this.initialize();
      }

      // Generate content with retries
      let lastError: Error | null = null;
      let retryCount = 0;

      for (let attempt = 0; attempt <= this.config.retries; attempt++) {
        try {
          const result = await this.model!.generateContent(validation.sanitizedInput || prompt);
          const response = await result.response;
          const text = response.text();

          const responseTime = Date.now() - startTime;

          // Cache the result if caching is enabled
          if (this.config.enableCaching && opts.cacheKey) {
            await this.cache.set(opts.cacheKey, text, {
              ttl: opts.cacheTTL || this.config.cacheTTL
            });
          }

          this.logger.info('AI content generated successfully', {
            userId: opts.userId,
            responseTime,
            model: this.config.model,
            retryCount,
            cached: false
          });

          return {
            success: true,
            data: text,
            cached: false,
            metadata: {
              responseTime,
              model: this.config.model,
              cacheHit: false,
              retryCount
            }
          };
        } catch (error) {
          lastError = error instanceof Error ? error : new Error(String(error));
          retryCount = attempt;
          
          if (attempt < this.config.retries) {
            this.logger.warn(`AI request attempt ${attempt + 1} failed, retrying`, {
              error: lastError.message,
              userId: opts.userId,
              attempt: attempt + 1
            });
            
            // Exponential backoff
            await this.delay(Math.pow(2, attempt) * 1000);
          }
        }
      }

      // All retries failed
      const responseTime = Date.now() - startTime;
      this.logger.error('AI content generation failed after all retries', lastError, {
        userId: opts.userId,
        responseTime,
        retryCount,
        prompt: prompt.substring(0, 100)
      });

      return {
        success: false,
        error: lastError?.message || 'Unknown error occurred',
        metadata: {
          responseTime,
          model: this.config.model,
          retryCount
        }
      };
    } catch (error) {
      const responseTime = Date.now() - startTime;
      this.logger.error('Unexpected error in AI content generation', error, {
        userId: opts.userId,
        responseTime
      });

      return {
        success: false,
        error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : String(error) : String(error) : String(error) : String(error) : String(error) : 'Unexpected error',
        metadata: {
          responseTime,
          model: this.config.model,
          retryCount: 0
        }
      };
    }
  }

  /**
   * Analyze skills gap using cached and optimized prompts
   */
  async analyzeSkillsGap(params: SkillGapParams): Promise<AIResponse> {
    const cacheKey = `skills-gap:${this.hashParams(params)}`;
    
    return this.cache.deduplicate(cacheKey, async () => {
      const prompt = this.buildSkillsGapPrompt(params);
      return this.generateContent(prompt, {
        userId: params.userId,
        cacheKey,
        cacheTTL: this.config.cacheTTL
      });
    });
  }

  /**
   * Generate career recommendations
   */
  async generateCareerRecommendations(params: CareerParams): Promise<AIResponse> {
    const cacheKey = `career-rec:${this.hashParams(params)}`;
    
    return this.cache.deduplicate(cacheKey, async () => {
      const prompt = this.buildCareerRecommendationsPrompt(params);
      return this.generateContent(prompt, {
        userId: params.userId,
        cacheKey,
        cacheTTL: this.config.cacheTTL
      });
    });
  }

  /**
   * Generate interview questions
   */
  async generateInterviewQuestions(params: InterviewParams): Promise<AIResponse> {
    const cacheKey = `interview:${this.hashParams(params)}`;
    
    return this.cache.deduplicate(cacheKey, async () => {
      const prompt = this.buildInterviewQuestionsPrompt(params);
      return this.generateContent(prompt, {
        userId: params.userId,
        cacheKey,
        cacheTTL: this.config.cacheTTL
      });
    });
  }

  /**
   * Analyze resume content
   */
  async analyzeResume(params: ResumeAnalysisParams): Promise<AIResponse> {
    const cacheKey = `resume:${this.hashParams(params)}`;
    
    return this.cache.deduplicate(cacheKey, async () => {
      const prompt = this.buildResumeAnalysisPrompt(params);
      return this.generateContent(prompt, {
        userId: params.userId,
        cacheKey,
        cacheTTL: this.config.cacheTTL
      });
    });
  }

  /**
   * Health check for the service
   */
  async healthCheck(): Promise<HealthStatus> {
    try {
      const startTime = Date.now();
      
      // Test AI service
      const testResponse = await this.generateContent('Health check test', {
        timeout: 5000
      });
      
      const responseTime = Date.now() - startTime;
      
      // Test cache service
      const cacheTestKey = 'health-check-test';
      await this.cache.set(cacheTestKey, 'test', { ttl: 1000 });
      const cacheResult = await this.cache.get(cacheTestKey);
      
      return {
        ai: testResponse.success,
        cache: {
          redis: true, // Would check actual Redis connection in production
          memory: cacheResult === 'test'
        },
        responseTime,
        lastCheck: new Date()
      };
    } catch (error) {
      this.logger.error('Health check failed', error);
      return {
        ai: false,
        cache: {
          redis: false,
          memory: false
        },
        lastCheck: new Date()
      };
    }
  }

  // Private helper methods

  private validateConfig(): void {
    if (!this.config.apiKey) {
      throw new Error('Gemini API key is required');
    }
    
    if (!this.config.model) {
      throw new Error('Gemini model is required');
    }
    
    if (this.config.timeout <= 0) {
      throw new Error('Timeout must be positive');
    }
    
    if (this.config.retries < 0) {
      throw new Error('Retries must be non-negative');
    }
  }

  private buildSkillsGapPrompt(params: SkillGapParams): string {
    return `Analyze the skills gap for career transition:

Current Skills: ${params.currentSkills.join(', ')}
Target Career Path: ${params.targetCareerPath}
Experience Level: ${params.experienceLevel}

Please provide:
1. Critical missing skills for the target career
2. Skills that transfer well from current skillset
3. Recommended learning path with priorities
4. Estimated timeline for skill development
5. Specific resources or certifications to pursue

Format the response as structured JSON with clear sections.`;
  }

  private buildCareerRecommendationsPrompt(params: CareerParams): string {
    return `Generate personalized career recommendations based on:

Assessment Data: ${JSON.stringify(params.assessmentData)}
Current Skills: ${params.skills.join(', ')}
Preferences: ${JSON.stringify(params.preferences)}

Please provide 3-5 specific career path recommendations with:
1. Career title and description
2. Match percentage with current profile
3. Required skills and qualifications
4. Growth potential and salary range
5. Steps to transition into this career

Format as structured JSON with detailed reasoning for each recommendation.`;
  }

  private buildInterviewQuestionsPrompt(params: InterviewParams): string {
    return `Generate ${params.count || 5} realistic interview questions for:

Career Path: ${params.careerPath}
Experience Level: ${params.experienceLevel}
Question Type: ${params.questionType}

For each question, provide:
1. The interview question
2. What the interviewer is looking for
3. Key points for a strong answer
4. Common mistakes to avoid

Format as structured JSON array with detailed guidance.`;
  }

  private buildResumeAnalysisPrompt(params: ResumeAnalysisParams): string {
    return `Analyze this resume and provide detailed feedback:

Resume Content:
${params.resumeText}

${params.targetRole ? `Target Role: ${params.targetRole}` : ''}

Please provide:
1. Overall strengths and weaknesses
2. Specific improvement suggestions
3. Missing keywords or skills
4. Formatting and structure feedback
5. ATS optimization recommendations
6. Quantified impact suggestions

Format as structured JSON with actionable recommendations.`;
  }

  private hashParams(params: any): string {
    // Simple hash function for cache keys
    return Buffer.from(JSON.stringify(params)).toString('base64').slice(0, 32);
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

/**
 * Factory function to create a decoupled Gemini service
 */
export function createGeminiService(config?: Partial<GeminiServiceConfig>): DecoupledGeminiService {
  const defaultConfig: GeminiServiceConfig = {
    provider: 'gemini',
    apiKey: process.env.GOOGLE_GEMINI_API_KEY || '',
    model: 'gemini-1.5-flash',
    timeout: AI_CONSTANTS.DEFAULT_TIMEOUT,
    retries: AI_CONSTANTS.DEFAULT_RETRIES,
    rateLimitPerMinute: AI_CONSTANTS.DEFAULT_RATE_LIMIT,
    enableCaching: true,
    cacheTTL: AI_CONSTANTS.DEFAULT_CACHE_TTL,
    enableMetrics: true,
    enableFallbacks: true,
    generationConfig: {
      temperature: 0.7,
      topK: 40,
      topP: 0.95,
      maxOutputTokens: 4000,
    }
  };

  const finalConfig = { ...defaultConfig, ...config };
  
  const cache = aiServiceFactory.createCacheService({
    provider: 'memory',
    maxMemorySize: 100,
    defaultTTL: finalConfig.cacheTTL,
    enableCompression: false,
    compressionThreshold: 1024,
    enableMetrics: true
  });
  
  const validator = aiServiceFactory.createValidator({
    maxPromptLength: AI_CONSTANTS.MAX_PROMPT_LENGTH,
    maxResponseLength: AI_CONSTANTS.MAX_RESPONSE_LENGTH,
    enableSecurityScan: true,
    blockedPatterns: [],
    allowedContentTypes: ['text/plain', 'application/json']
  });
  
  const logger = aiServiceFactory.createLogger();

  return new DecoupledGeminiService(finalConfig, cache, validator, logger);
}

/**
 * Default decoupled Gemini service instance
 */
export const decoupledGeminiService = createGeminiService();
