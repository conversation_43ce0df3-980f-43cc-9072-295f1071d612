import { GoogleGenerativeAI, GenerativeModel, GenerationConfig } from '@google/generative-ai';
import { consolidatedCache } from '@/lib/services/consolidated-cache-service';
// Temporarily disable advanced modules to fix import issues
// import redisCache from '@/lib/redis-cache';
import AIInputValidator from '@/lib/ai-input-validator';
// import AIResponseParser from '@/lib/ai-response-parser';
// import { aiServiceMonitor } from '@/lib/ai-service-monitor';
// import { advancedCacheManager } from '@/lib/advanced-cache-manager';
// import { requestOptimizer } from '@/lib/request-optimizer';
// import { performanceMonitor } from '@/lib/performance-monitor';

// Circuit Breaker for external AI service calls
interface CircuitBreakerState {
  isOpen: boolean;
  failureCount: number;
  lastFailureTime: number;
  successCount: number;
}

class CircuitBreaker {
  private state: CircuitBreakerState = {
    isOpen: false,
    failureCount: 0,
    lastFailureTime: 0,
    successCount: 0
  };

  private readonly failureThreshold = 5;
  private readonly recoveryTimeout = 60000; // 1 minute

  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state.isOpen) {
      if (Date.now() - this.state.lastFailureTime > this.recoveryTimeout) {
        this.state.isOpen = false;
        this.state.failureCount = 0;
      } else {
        throw new Error('Circuit breaker is open - service temporarily unavailable');
      }
    }

    try {
      const result = await operation();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }

  private onSuccess(): void {
    this.state.successCount++;
    this.state.failureCount = 0;
  }

  private onFailure(): void {
    this.state.failureCount++;
    this.state.lastFailureTime = Date.now();

    if (this.state.failureCount >= this.failureThreshold) {
      this.state.isOpen = true;
      AIServiceLogger.warn('Circuit breaker opened due to repeated failures');
    }
  }

  getState(): CircuitBreakerState {
    return { ...this.state };
  }
}

const geminiCircuitBreaker = new CircuitBreaker();

// Enhanced logging utility for AI service
export class AIServiceLogger {
  private static logLevel = process.env.NODE_ENV === 'development' ? 'debug' : 'info';

  static info(message: string, context?: any) {
    console.log(`[AI-Service] ${new Date().toISOString()} INFO: ${message}`, context || '');
  }

  static warn(message: string, context?: any) {
    console.warn(`[AI-Service] ${new Date().toISOString()} WARN: ${message}`, context || '');
  }

  static error(message: string, error?: any, context?: any) {
    console.error(`[AI-Service] ${new Date().toISOString()} ERROR: ${message}`, {
      error: error instanceof Error ? {
        message: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : String(error) : String(error) : String(error) : String(error) : String(error) : String(error) : String(error),
        stack: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.stack : String(error) : String(error) : String(error) : String(error) : String(error) : String(error),
        name: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.name : String(error) : String(error) : String(error) : String(error) : String(error) : String(error)
      } : error,
      context
    });
  }

  static debug(message: string, context?: any) {
    if (this.logLevel === 'debug') {
      console.log(`[AI-Service] ${new Date().toISOString()} DEBUG: ${message}`, context || '');
    }
  }
}

// Validate API key before initialization
const apiKey = process.env.GOOGLE_GEMINI_API_KEY;
if (!apiKey || apiKey.length < 10) {
  AIServiceLogger.error('CRITICAL: Invalid or missing GOOGLE_GEMINI_API_KEY environment variable');
  throw new Error('Gemini AI service cannot be initialized: Invalid API key configuration');
}

// Initialize Google Gemini AI
const genAI = new GoogleGenerativeAI(apiKey);

// Configuration for different AI tasks
const configs: Record<string, GenerationConfig> = {
  resume_analysis: {
    temperature: 0.3,
    topK: 40,
    topP: 0.95,
    maxOutputTokens: 2048,
  },
  career_recommendations: {
    temperature: 0.7,
    topK: 40,
    topP: 0.95,
    maxOutputTokens: 1024,
  },
  skills_analysis: {
    temperature: 0.4,
    topK: 40,
    topP: 0.95,
    maxOutputTokens: 1536,
  },
  interview_prep: {
    temperature: 0.6,
    topK: 40,
    topP: 0.95,
    maxOutputTokens: 2048,
  },
  interview_questions: {
    temperature: 0.7,
    topK: 40,
    topP: 0.95,
    maxOutputTokens: 3072,
  },
  interview_analysis: {
    temperature: 0.4,
    topK: 40,
    topP: 0.95,
    maxOutputTokens: 2048,
  },
};

export interface AIResponse {
  success: boolean;
  data?: any;
  error?: string;
  cached?: boolean;
}

export interface ResumeAnalysisResult {
  strengths: string[];
  weaknesses: string[];
  suggestions: string[];
  skillsIdentified: string[];
  experienceLevel: 'entry' | 'mid' | 'senior' | 'executive';
  industryFit: string[];
  overallScore: number;
}

export interface CareerRecommendation {
  careerPath: string;
  matchScore: number;
  reasoning: string;
  requiredSkills: string[];
  timeToTransition: string;
  salaryRange: string;
  growthPotential: string;
}

export interface SkillsAnalysisResult {
  currentSkills: string[];
  skillGaps: string[];
  learningRecommendations: {
    skill: string;
    priority: 'high' | 'medium' | 'low';
    estimatedTime: string;
    resources: string[];
  }[];
  careerReadiness: number;
}

export interface InterviewPrepResult {
  commonQuestions: {
    question: string;
    category: 'behavioral' | 'technical' | 'situational';
    sampleAnswer: string;
    tips: string[];
  }[];
  industrySpecificQuestions: {
    question: string;
    difficulty: 'easy' | 'medium' | 'hard';
    keyPoints: string[];
  }[];
  preparationTips: string[];
}

export interface InterviewQuestionsResult {
  questions: {
    questionText: string;
    questionType: string;
    category: string;
    difficulty: string;
    expectedDuration: number;
    context: string;
    hints: any;
    followUpQuestions: any;
    industrySpecific: boolean;
    tags: any;
    isRequired: boolean;
  }[];
  metadata: {
    sessionType: string;
    totalQuestions: number;
    difficultyDistribution: any;
    categoryDistribution: any;
  };
}

export interface InterviewResponseAnalysis {
  overallScore: number;
  analysis: any;
  feedback: any;
  strengths: any;
  improvements: any;
  starMethodScore?: number;
  confidenceLevel?: number;
  communicationScore?: number;
  technicalScore?: number;
}

// Response validation schemas
const RESPONSE_SCHEMAS = {
  resume_analysis: {
    type: 'object' as const,
    required: ['strengths', 'weaknesses', 'suggestions', 'overallScore'],
    properties: {
      strengths: { type: 'array' as const, items: { type: 'string' as const } },
      weaknesses: { type: 'array' as const, items: { type: 'string' as const } },
      suggestions: { type: 'array' as const, items: { type: 'string' as const } },
      skillsIdentified: { type: 'array' as const, items: { type: 'string' as const } },
      experienceLevel: { type: 'string' as const, enum: ['entry', 'mid', 'senior', 'executive'] },
      industryFit: { type: 'array' as const, items: { type: 'string' as const } },
      overallScore: { type: 'number' as const, min: 1, max: 100 }
    }
  },
  career_recommendations: {
    type: 'object' as const,
    required: ['recommendations'],
    properties: {
      recommendations: {
        type: 'array' as const,
        items: {
          type: 'object' as const,
          required: ['careerPath', 'matchScore', 'reasoning'],
          properties: {
            careerPath: { type: 'string' as const, minLength: 1, maxLength: 200 },
            matchScore: { type: 'number' as const, min: 1, max: 100 },
            reasoning: { type: 'string' as const, minLength: 10, maxLength: 1000 },
            requiredSkills: { type: 'array' as const, items: { type: 'string' as const } },
            timeToTransition: { type: 'string' as const },
            salaryRange: { type: 'string' as const },
            growthPotential: { type: 'string' as const }
          }
        }
      }
    }
  },
  interview_questions: {
    type: 'object' as const,
    required: ['questions'],
    properties: {
      questions: {
        type: 'array' as const,
        items: {
          type: 'object' as const,
          required: ['questionText', 'questionType', 'category', 'difficulty'],
          properties: {
            questionText: { type: 'string' as const, minLength: 10, maxLength: 1000 },
            questionType: { type: 'string' as const },
            category: { type: 'string' as const },
            difficulty: { type: 'string' as const, enum: ['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT'] },
            expectedDuration: { type: 'number' as const, min: 30, max: 600 },
            context: { type: 'string' as const },
            hints: { type: 'object' as const },
            followUpQuestions: { type: 'array' as const },
            industrySpecific: { type: 'boolean' as const },
            tags: { type: 'array' as const },
            isRequired: { type: 'boolean' as const }
          }
        }
      },
      metadata: { type: 'object' as const }
    }
  },
  interview_analysis: {
    type: 'object' as const,
    required: ['overallScore', 'analysis', 'feedback'],
    properties: {
      overallScore: { type: 'number' as const, min: 1, max: 10 },
      analysis: { type: 'object' as const },
      feedback: { type: 'object' as const },
      strengths: { type: 'array' as const },
      improvements: { type: 'array' as const },
      starMethodScore: { type: 'number' as const, min: 1, max: 10 },
      confidenceLevel: { type: 'number' as const, min: 0, max: 1 },
      communicationScore: { type: 'number' as const, min: 1, max: 10 },
      technicalScore: { type: 'number' as const, min: 1, max: 10 }
    }
  }
};

interface RateLimitEntry {
  count: number;
  resetTime: number;
}

class GeminiService {
  private model: GenerativeModel;
  private cacheEnabled: boolean;
  private cacheTTL: number;
  private rateLimiter: Map<string, RateLimitEntry>;
  private readonly RATE_LIMIT_REQUESTS_PER_MINUTE = 60; // Adjust based on your API limits
  private readonly RATE_LIMIT_WINDOW_MS = 60 * 1000; // 1 minute

  constructor() {
    try {
      this.model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });
      this.cacheEnabled = process.env.NODE_ENV === 'production';
      this.rateLimiter = new Map();

      // Validate and set cache TTL
      const cacheTTLStr = process.env.AI_CACHE_TTL || '3600';
      const parsedTTL = parseInt(cacheTTLStr);
      if (isNaN(parsedTTL) || parsedTTL < 0) {
        AIServiceLogger.warn('Invalid AI_CACHE_TTL value, using default 3600 seconds');
        this.cacheTTL = 3600;
      } else {
        this.cacheTTL = parsedTTL;
      }

      // Clean up rate limiter every 5 minutes
      setInterval(() => this.cleanupRateLimiter(), 5 * 60 * 1000);

      // Initialize performance optimizations (temporarily disabled)
      // this.initializePerformanceOptimizations();

      AIServiceLogger.info('Gemini AI Service initialized successfully', {
        model: 'gemini-1.5-flash',
        cacheEnabled: this.cacheEnabled,
        cacheTTL: this.cacheTTL,
        rateLimitPerMinute: this.RATE_LIMIT_REQUESTS_PER_MINUTE,
        performanceOptimizationsEnabled: false
      });
    } catch (error) {
      AIServiceLogger.error('Failed to initialize Gemini AI Service', error);
      throw new Error('Gemini AI service initialization failed');
    }
  }

  /**
   * Initialize the service (for compatibility with tests)
   */
  async initialize(): Promise<{ success: boolean; message?: string }> {
    try {
      // Perform any async initialization if needed
      await this.initializePerformanceOptimizations();

      AIServiceLogger.info('Gemini service initialization completed');
      return { success: true, message: 'Service initialized successfully' };
    } catch (error) {
      AIServiceLogger.error('Failed to initialize Gemini service', error);
      return { success: false, message: error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : String(error) : String(error) : String(error) : String(error) : String(error) : String(error) : String(error) : 'Unknown error' };
    }
  }

  private async initializePerformanceOptimizations(): Promise<void> {
    try {
      // Initialize advanced cache manager (temporarily disabled)
      // await advancedCacheManager.initialize();

      // Start performance monitoring (temporarily disabled)
      // performanceMonitor.recordMetric('service_initialization', 1, {
      //   timestamp: Date.now(),
      //   component: 'gemini-service'
      // });

      AIServiceLogger.info('Performance optimizations initialized successfully (basic mode)');
    } catch (error) {
      AIServiceLogger.error('Failed to initialize performance optimizations', error);
    }
  }

  private checkRateLimit(userId: string = 'anonymous'): boolean {
    const now = Date.now();
    const userLimit = this.rateLimiter.get(userId);

    if (!userLimit) {
      // First request for this user
      this.rateLimiter.set(userId, {
        count: 1,
        resetTime: now + this.RATE_LIMIT_WINDOW_MS
      });
      AIServiceLogger.debug('Rate limit initialized for user', { userId, count: 1 });
      return true;
    }

    // Check if window has expired
    if (now > userLimit.resetTime) {
      // Reset the window
      this.rateLimiter.set(userId, {
        count: 1,
        resetTime: now + this.RATE_LIMIT_WINDOW_MS
      });
      AIServiceLogger.debug('Rate limit window reset for user', { userId, count: 1 });
      return true;
    }

    // Check if under limit
    if (userLimit.count < this.RATE_LIMIT_REQUESTS_PER_MINUTE) {
      userLimit.count++;
      AIServiceLogger.debug('Rate limit check passed', { userId, count: userLimit.count, limit: this.RATE_LIMIT_REQUESTS_PER_MINUTE });
      return true;
    }

    AIServiceLogger.warn('Rate limit exceeded', { userId, count: userLimit.count, limit: this.RATE_LIMIT_REQUESTS_PER_MINUTE });
    return false; // Rate limit exceeded
  }

  private cleanupRateLimiter(): void {
    const now = Date.now();
    for (const [userId, limit] of Array.from(this.rateLimiter.entries())) {
      if (now > limit.resetTime) {
        this.rateLimiter.delete(userId);
      }
    }
  }

  private generateFallbackData(configKey: string): any {
    switch (configKey) {
      case 'resume_analysis':
        return {
          strengths: ['Professional presentation', 'Clear work history'],
          weaknesses: ['Could include more specific achievements', 'Skills section could be expanded'],
          suggestions: ['Add quantifiable results to work experience', 'Include relevant certifications'],
          skillsIdentified: ['Communication', 'Problem solving', 'Teamwork'],
          experienceLevel: 'mid',
          industryFit: ['Technology', 'Business'],
          overallScore: 75
        };

      case 'career_recommendations':
        return {
          recommendations: [
            {
              careerPath: 'Software Developer',
              matchScore: 80,
              reasoning: 'Your technical skills and problem-solving abilities align well with software development roles.',
              requiredSkills: ['Programming', 'Software Design', 'Testing'],
              timeToTransition: '6-12 months',
              salaryRange: '$60,000 - $100,000',
              growthPotential: 'High'
            }
          ]
        };

      case 'interview_questions':
        return {
          questions: [
            {
              questionText: 'Tell me about yourself and your professional background.',
              questionType: 'BEHAVIORAL',
              category: 'GENERAL',
              difficulty: 'INTERMEDIATE',
              expectedDuration: 180,
              context: 'This is a common opening question to assess communication skills.',
              hints: {
                structure: 'Use a brief professional summary highlighting relevant experience',
                keyPoints: ['Current role', 'Key achievements', 'Career goals']
              },
              followUpQuestions: ['What interests you most about this role?'],
              industrySpecific: false,
              tags: ['opening', 'general'],
              isRequired: true
            }
          ],
          metadata: {
            sessionType: 'GENERAL',
            totalQuestions: 1,
            difficultyDistribution: { INTERMEDIATE: 1 },
            categoryDistribution: { GENERAL: 1 }
          }
        };

      case 'interview_analysis':
        return {
          overallScore: 6,
          analysis: {
            contentQuality: { score: 6, feedback: 'Response provides adequate detail' },
            structure: { score: 5, feedback: 'Could benefit from better organization' },
            relevance: { score: 7, feedback: 'Addresses the question appropriately' }
          },
          feedback: {
            positive: ['Clear communication', 'Relevant examples'],
            constructive: ['Could provide more specific details', 'Consider using STAR method']
          },
          strengths: ['Good communication skills', 'Relevant experience mentioned'],
          improvements: [
            { area: 'Structure', suggestion: 'Use STAR method for behavioral questions', priority: 'high' }
          ],
          starMethodScore: 5,
          confidenceLevel: 0.7,
          communicationScore: 7,
          technicalScore: 6
        };

      default:
        return { content: 'Fallback response generated due to parsing failure' };
    }
  }

  private async generateContent(
    prompt: string,
    configKey: string,
    cacheKey?: string,
    timeoutMs: number = 30000,
    userId?: string
  ): Promise<AIResponse> {
    const startTime = Date.now();
    let cacheHit = false;

    // Record performance metrics (temporarily disabled)
    // performanceMonitor.recordMetric(`${configKey}_request_start`, 1, {
    //   userId,
    //   promptLength: prompt.length
    // });

    try {
      // Check rate limit first
      if (!this.checkRateLimit(userId || 'anonymous')) {
        // aiServiceMonitor.recordRateLimitHit(userId || 'anonymous');
        return {
          success: false,
          error: 'Rate limit exceeded. Please wait before making another request.',
        };
      }

      // Check cache first if enabled (using consolidated cache)
      if (this.cacheEnabled && cacheKey) {
        const cached = await consolidatedCache.get<any>(cacheKey);
        if (cached) {
          cacheHit = true;
          const responseTime = Date.now() - startTime;

          // Record performance metrics (temporarily disabled)
          // performanceMonitor.recordResponseTime(configKey, responseTime, userId);
          // performanceMonitor.recordCacheHit(configKey, true);

          AIServiceLogger.debug('Basic cache hit', {
            cacheKey: cacheKey.substring(0, 50) + '...',
            configKey,
            responseTime
          });

          // Record cache hit in monitoring (temporarily disabled)
          // aiServiceMonitor.recordOperation(configKey, responseTime, true, true, userId);

          return {
            success: true,
            data: cached,
            cached: true,
          };
        } else {
          // performanceMonitor.recordCacheHit(configKey, false);
          AIServiceLogger.debug('Basic cache miss', {
            cacheKey: cacheKey.substring(0, 50) + '...',
            configKey
          });
        }
      }

      // Configure the model for this specific task
      const config = configs[configKey] || configs.career_recommendations;

      // Create timeout promise
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('AI request timeout')), timeoutMs);
      });

      // Execute with timeout (simplified without request optimizer)
      const result = await Promise.race([
        this.model.generateContent({
          contents: [{ role: 'user', parts: [{ text: prompt }] }],
          generationConfig: config,
        }),
        timeoutPromise
      ]);

      const response = await result.response;
      const text = response.text();

      // Security validation
      if (!text) {
        return {
          success: false,
          error: 'Empty response from AI service',
        };
      }

      if (text.length > 50000) {
        return {
          success: false,
          error: 'AI response too large',
        };
      }

      if (this.containsHarmfulContent(text)) {
        return {
          success: false,
          error: 'AI response contains inappropriate content',
        };
      }

      // Parse response using basic JSON parsing (simplified)
      let data;
      try {
        // Try to parse as JSON first
        data = JSON.parse(text);
      } catch (jsonError) {
        // If JSON parsing fails, try to extract JSON from the text
        const jsonMatch = text.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          try {
            data = JSON.parse(jsonMatch[0]);
          } catch (extractError) {
            AIServiceLogger.error('Failed to parse AI response as JSON', extractError, {
              configKey,
              responseLength: text.length
            });
            return {
              success: false,
              error: 'Failed to parse AI response as valid JSON',
            };
          }
        } else {
          // Return raw text if no JSON found
          data = { response: text };
        }
      }

      // Log parsing success
      AIServiceLogger.debug('AI response parsed successfully', {
        configKey,
        parseMethod: 'basic_json',
        responseLength: text.length
      });

      // Post-process data to ensure required fields are present
      if (configKey === 'interview_analysis') {
        data = this.ensureInterviewAnalysisFields(data);
      }

      // Cache the result if enabled (using consolidated cache)
      if (this.cacheEnabled && cacheKey) {
        await consolidatedCache.set(cacheKey, data, {
          ttl: this.cacheTTL * 1000, // Convert seconds to milliseconds
          tags: ['ai_service', 'gemini', configKey]
        });
        AIServiceLogger.debug('Response cached in consolidated cache', {
          cacheKey: cacheKey.substring(0, 50) + '...',
          configKey,
          ttl: this.cacheTTL
        });
      }

      const responseTime = Date.now() - startTime;

      AIServiceLogger.info('AI request completed successfully', {
        configKey,
        responseSize: JSON.stringify(data).length,
        userId: userId || 'anonymous',
        cached: false,
        responseTime
      });

      // Record performance metrics (temporarily disabled)
      // performanceMonitor.recordResponseTime(configKey, responseTime, userId);
      // performanceMonitor.recordMetric(`${configKey}_success`, 1, { userId });

      // Record successful operation in monitoring (temporarily disabled)
      // aiServiceMonitor.recordOperation(configKey, responseTime, true, cacheHit, userId);

      return {
        success: true,
        data,
        cached: false,
      };
    } catch (error) {
      const errorContext = {
        operation: 'generateContent',
        configKey,
        cacheKey: cacheKey ? cacheKey.substring(0, 50) + '...' : undefined,
        timeoutMs,
        userId: userId || 'anonymous'
      };

      AIServiceLogger.error('Gemini AI request failed', error, errorContext);

      // Categorize error types for better handling
      let errorMessage = 'AI service unavailable';
      let errorType = 'unknown';

      if (error instanceof Error) {
        if (error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : String(error) : String(error) : String(error) : String(error) : String(error) : String(error) : String(error).includes('timeout')) {
          errorMessage = 'AI request timed out - please try again';
          errorType = 'timeout';
        } else if (error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : String(error) : String(error) : String(error) : String(error) : String(error) : String(error) : String(error).includes('quota') || error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : String(error) : String(error) : String(error) : String(error) : String(error) : String(error) : String(error).includes('limit')) {
          errorMessage = 'AI service quota exceeded - please try again later';
          errorType = 'quota_exceeded';
        } else if (error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : String(error) : String(error) : String(error) : String(error) : String(error) : String(error) : String(error).includes('authentication') || error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : String(error) : String(error) : String(error) : String(error) : String(error) : String(error) : String(error).includes('unauthorized')) {
          errorMessage = 'AI service authentication failed';
          errorType = 'auth_failed';
        } else if (error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : String(error) : String(error) : String(error) : String(error) : String(error) : String(error) : String(error).includes('network') || error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : String(error) : String(error) : String(error) : String(error) : String(error) : String(error) : String(error).includes('connection')) {
          errorMessage = 'Network connection failed - please check your internet connection';
          errorType = 'network_error';
        } else {
          errorMessage = error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : String(error) : String(error) : String(error) : String(error) : String(error) : String(error) : String(error);
          errorType = 'api_error';
        }
      }

      // Log error metrics for monitoring
      AIServiceLogger.debug('Error categorized', { errorType, configKey, userId });

      // Record performance metrics for errors (temporarily disabled)
      const responseTime = Date.now() - startTime;
      // performanceMonitor.recordResponseTime(configKey, responseTime, userId);
      // performanceMonitor.recordError(configKey, errorType, userId);

      // Record failed operation in monitoring (temporarily disabled)
      // aiServiceMonitor.recordOperation(configKey, responseTime, false, cacheHit, userId, errorMessage);

      return {
        success: false,
        error: errorMessage,
      };
    }
  }

  async analyzeResume(resumeText: string, userId?: string): Promise<AIResponse> {
    // Basic validation (simplified without AIInputValidator)
    if (!resumeText || resumeText.trim().length === 0) {
      AIServiceLogger.warn('Resume analysis input validation failed - empty text', {
        userId: userId || 'anonymous'
      });
      return {
        success: false,
        error: 'Resume text cannot be empty'
      };
    }

    if (resumeText.length > 50000) {
      AIServiceLogger.warn('Resume analysis input validation failed - too long', {
        userId: userId || 'anonymous'
      });
      return {
        success: false,
        error: 'Resume text is too long (max 50,000 characters)'
      };
    }

    // Basic security check for obvious harmful content
    const harmfulPatterns = ['<script', 'javascript:', 'data:text/html'];
    const hasHarmfulContent = harmfulPatterns.some(pattern =>
      resumeText.toLowerCase().includes(pattern.toLowerCase())
    );

    if (hasHarmfulContent) {
      AIServiceLogger.error('Potentially harmful content detected in resume analysis', {
        userId: userId || 'anonymous'
      });
      return {
        success: false,
        error: 'Resume content contains potentially harmful elements'
      };
    }

    const sanitizedResumeText = resumeText.trim();

    const prompt = `
Analyze the following resume and provide a comprehensive assessment in JSON format:

Resume Text:
${sanitizedResumeText}

Please provide your analysis in the following JSON structure:
{
  "strengths": ["list of key strengths"],
  "weaknesses": ["areas for improvement"],
  "suggestions": ["specific actionable suggestions"],
  "skillsIdentified": ["list of skills found in resume"],
  "experienceLevel": "entry|mid|senior|executive",
  "industryFit": ["industries this person would fit well in"],
  "overallScore": number between 1-100
}

Focus on:
1. Professional presentation and formatting
2. Skills and experience relevance
3. Career progression and achievements
4. Areas for improvement
5. Industry alignment
`;

    const cacheKey = userId ? `resume_analysis:${userId}:${Buffer.from(resumeText).toString('base64').slice(0, 32)}` : undefined;

    return this.generateContent(prompt, 'resume_analysis', cacheKey, 30000, userId);
  }

  async generateCareerRecommendations(
    assessmentData: any,
    currentSkills: string[],
    preferences: any,
    userId?: string
  ): Promise<AIResponse> {
    // Basic validation for skills array
    if (!Array.isArray(currentSkills)) {
      AIServiceLogger.warn('Career recommendations skills validation failed - not array', {
        userId: userId || 'anonymous'
      });
      return {
        success: false,
        error: 'Skills must be provided as an array'
      };
    }

    if (currentSkills.length === 0) {
      AIServiceLogger.warn('Career recommendations skills validation failed - empty array', {
        userId: userId || 'anonymous'
      });
      return {
        success: false,
        error: 'At least one skill must be provided'
      };
    }

    const sanitizedSkills = currentSkills.filter(skill =>
      typeof skill === 'string' && skill.trim().length > 0
    ).map(skill => skill.trim());
    const prompt = `
Based on the following career assessment data, current skills, and preferences, provide personalized career path recommendations in JSON format:

Assessment Data:
${JSON.stringify(assessmentData, null, 2)}

Current Skills:
${sanitizedSkills.join(', ')}

Preferences:
${JSON.stringify(preferences, null, 2)}

Please provide 3-5 career recommendations in the following JSON structure:
{
  "recommendations": [
    {
      "careerPath": "specific career title",
      "matchScore": number between 1-100,
      "reasoning": "detailed explanation of why this fits",
      "requiredSkills": ["skills needed for this career"],
      "timeToTransition": "estimated time to transition",
      "salaryRange": "typical salary range",
      "growthPotential": "career growth prospects"
    }
  ]
}

Consider:
1. Skills alignment and transferability
2. Personal values and work preferences
3. Market demand and growth potential
4. Realistic transition timeline
5. Salary expectations and growth
`;

    const cacheKey = userId ? `career_recommendations:${userId}:${Date.now().toString().slice(-8)}` : undefined;

    return this.generateContent(prompt, 'career_recommendations', cacheKey, 30000, userId);
  }

  async analyzeSkillsGap(
    currentSkills: string[],
    targetCareerPath: string,
    experienceLevel: string,
    userId?: string
  ): Promise<AIResponse> {
    const prompt = `
Analyze the skills gap for transitioning to a specific career path and provide learning recommendations in JSON format:

Current Skills:
${currentSkills.join(', ')}

Target Career Path:
${targetCareerPath}

Experience Level:
${experienceLevel}

Please provide your analysis in the following JSON structure:
{
  "currentSkills": ["validated current skills relevant to target career"],
  "skillGaps": ["skills needed but not currently possessed"],
  "learningRecommendations": [
    {
      "skill": "specific skill name",
      "priority": "high|medium|low",
      "estimatedTime": "time to learn this skill",
      "resources": ["recommended learning resources or types"]
    }
  ],
  "careerReadiness": number between 1-100
}

Focus on:
1. Most critical skills for the target career
2. Realistic learning timelines
3. Prioritization based on impact
4. Practical learning approaches
5. Current market requirements
`;

    const cacheKey = userId ? `skills_analysis:${userId}:${targetCareerPath.replace(/\s+/g, '_')}` : undefined;

    return this.generateContent(prompt, 'skills_analysis', cacheKey, 30000, userId);
  }

  async analyzeComprehensiveSkillGap(
    currentSkills: Array<{
      skillName: string;
      selfRating: number;
      confidenceLevel: number;
      lastUsed?: string;
      yearsOfExperience?: number;
    }>,
    targetCareerPath: {
      careerPathId?: string;
      careerPathName: string;
      targetLevel: string;
    },
    preferences: {
      timeframe: string;
      hoursPerWeek: number;
      learningStyle: string[];
      budget: string;
      focusAreas: string[];
    },
    careerPathData: any,
    userId?: string
  ): Promise<AIResponse> {
    const prompt = `
Perform a comprehensive skill gap analysis and generate a detailed learning plan in JSON format:

CURRENT SKILLS ASSESSMENT:
${currentSkills.map(skill => `
- ${skill.skillName}: Self-Rating ${skill.selfRating}/10, Confidence ${skill.confidenceLevel}/10
  ${skill.yearsOfExperience ? `Experience: ${skill.yearsOfExperience} years` : ''}
  ${skill.lastUsed ? `Last Used: ${skill.lastUsed}` : ''}
`).join('')}

TARGET CAREER PATH:
- Career: ${targetCareerPath.careerPathName}
- Target Level: ${targetCareerPath.targetLevel}
- Required Skills: ${careerPathData?.requiredSkills?.map((s: any) => s.name).join(', ') || 'Not specified'}

LEARNING PREFERENCES:
- Timeframe: ${preferences.timeframe}
- Available Hours/Week: ${preferences.hoursPerWeek}
- Learning Style: ${preferences.learningStyle.join(', ')}
- Budget: ${preferences.budget}
- Focus Areas: ${preferences.focusAreas.join(', ')}

AVAILABLE RESOURCES:
${careerPathData?.learningResources?.slice(0, 10).map((resource: any) => `
- ${resource.title} (${resource.type}) - ${resource.skillLevel} level
  Skills: ${resource.skills?.join(', ') || 'General'}
  Cost: ${resource.cost || 'Free'}
`).join('') || 'No specific resources available'}

Please provide your comprehensive analysis in the following JSON structure:
{
  "skillGaps": [
    {
      "skillId": "generated-id",
      "skillName": "specific skill name",
      "currentLevel": number 1-10,
      "targetLevel": number 1-10,
      "gapSeverity": "CRITICAL|HIGH|MEDIUM|LOW",
      "priority": number 1-100,
      "estimatedLearningTime": number in hours,
      "marketDemand": "VERY_HIGH|HIGH|MODERATE|LOW|VERY_LOW",
      "salaryImpact": number percentage increase
    }
  ],
  "learningPlan": {
    "totalEstimatedHours": number,
    "milestones": [
      {
        "month": number,
        "skills": ["skill names to focus on"],
        "estimatedHours": number,
        "learningPaths": ["recommended learning path names"],
        "objectives": ["specific learning objectives"],
        "assessmentCriteria": ["how to measure progress"]
      }
    ],
    "recommendedResources": [
      {
        "resourceId": "resource-id",
        "resourceType": "COURSE|BOOK|PROJECT|CERTIFICATION|TUTORIAL",
        "priority": "CRITICAL|HIGH|MEDIUM|LOW|OPTIONAL",
        "skillsAddressed": ["skill names"],
        "estimatedHours": number,
        "cost": "FREE|FREEMIUM|PAID",
        "difficulty": "BEGINNER|INTERMEDIATE|ADVANCED|EXPERT",
        "prerequisites": ["prerequisite skills"]
      }
    ]
  },
  "careerReadiness": {
    "currentScore": number 0-100,
    "targetScore": number 0-100,
    "improvementPotential": number 0-100,
    "timeToTarget": number in months,
    "confidenceLevel": number 0-100,
    "marketCompetitiveness": number 0-100
  },
  "marketInsights": {
    "industryTrends": [
      {
        "skill": "skill name",
        "trend": "DECLINING|STABLE|GROWING|RAPIDLY_GROWING|EMERGING",
        "demandLevel": "VERY_HIGH|HIGH|MODERATE|LOW|VERY_LOW",
        "futureOutlook": "description of future prospects"
      }
    ],
    "salaryProjections": {
      "currentEstimate": number,
      "targetEstimate": number,
      "improvementPotential": number percentage,
      "timeToRealization": number in months
    },
    "competitiveAdvantage": [
      {
        "skill": "skill name",
        "advantage": "description of competitive advantage",
        "rarity": "COMMON|UNCOMMON|RARE|VERY_RARE"
      }
    ]
  }
}

ANALYSIS GUIDELINES:
1. Be realistic about learning timelines based on available hours
2. Prioritize skills with highest impact on career goals
3. Consider market demand and salary impact
4. Account for learning style preferences
5. Suggest progressive skill building (prerequisites first)
6. Include both technical and soft skills
7. Provide actionable, specific recommendations
8. Consider budget constraints when suggesting resources
9. Factor in skill obsolescence and future trends
10. Personalize recommendations based on current skill levels

Focus on creating a comprehensive, actionable plan that maximizes career advancement potential within the specified timeframe and constraints.
`;

    const cacheKey = userId ? `comprehensive_skills_analysis:${userId}:${targetCareerPath.careerPathName.replace(/\s+/g, '_')}_${targetCareerPath.targetLevel}_${preferences.timeframe}` : undefined;

    return this.generateContent(prompt, 'comprehensive_skills_analysis', cacheKey, 45000, userId);
  }

  async generatePersonalizedLearningPlan(
    skillGaps: Array<{
      skillName: string;
      currentLevel: number;
      targetLevel: number;
      gapSeverity: string;
      priority: number;
      estimatedLearningTime: number;
    }>,
    userPreferences: {
      timeframe: string;
      hoursPerWeek: number;
      learningStyle: string[];
      budget: string;
      focusAreas: string[];
    },
    marketData: Array<{
      skillName: string;
      demandLevel: string;
      growthTrend: string;
      salaryImpact?: number;
    }>,
    userId?: string
  ): Promise<AIResponse> {
    const prompt = `
Generate a personalized learning plan optimized for the user's specific needs and constraints in JSON format:

SKILL GAPS TO ADDRESS:
${skillGaps.map(gap => `
- ${gap.skillName}: Current ${gap.currentLevel}/10 → Target ${gap.targetLevel}/10
  Gap Severity: ${gap.gapSeverity}, Priority: ${gap.priority}/100
  Estimated Learning Time: ${gap.estimatedLearningTime} hours
`).join('')}

USER PREFERENCES:
- Timeframe: ${userPreferences.timeframe}
- Available Hours/Week: ${userPreferences.hoursPerWeek}
- Learning Style: ${userPreferences.learningStyle.join(', ')}
- Budget: ${userPreferences.budget}
- Focus Areas: ${userPreferences.focusAreas.join(', ')}

MARKET DATA:
${marketData.map(data => `
- ${data.skillName}: Demand ${data.demandLevel}, Trend ${data.growthTrend}
  ${data.salaryImpact ? `Salary Impact: +${data.salaryImpact}%` : ''}
`).join('')}

Please provide a personalized learning plan in the following JSON structure:
{
  "learningPlan": {
    "totalDuration": "timeframe description",
    "totalHours": number,
    "weeklyCommitment": number,
    "phases": [
      {
        "phaseNumber": number,
        "phaseName": "phase name",
        "duration": "duration description",
        "objectives": ["specific learning objectives"],
        "skills": [
          {
            "skillName": "skill name",
            "currentLevel": number,
            "targetLevel": number,
            "hoursAllocated": number,
            "learningApproach": "description of approach",
            "resources": [
              {
                "type": "COURSE|BOOK|PROJECT|TUTORIAL|PRACTICE",
                "title": "resource title",
                "description": "resource description",
                "estimatedHours": number,
                "cost": "FREE|FREEMIUM|PAID",
                "difficulty": "BEGINNER|INTERMEDIATE|ADVANCED",
                "learningStyle": ["VISUAL|AUDITORY|KINESTHETIC|READING"],
                "priority": "HIGH|MEDIUM|LOW"
              }
            ],
            "milestones": [
              {
                "week": number,
                "milestone": "milestone description",
                "assessmentMethod": "how to assess progress"
              }
            ]
          }
        ],
        "practiceProjects": [
          {
            "projectName": "project name",
            "description": "project description",
            "skillsApplied": ["skill names"],
            "estimatedHours": number,
            "difficulty": "BEGINNER|INTERMEDIATE|ADVANCED",
            "deliverables": ["expected deliverables"]
          }
        ]
      }
    ]
  }
}

OPTIMIZATION GUIDELINES:
1. Prioritize high-impact, high-demand skills first
2. Balance quick wins with long-term skill development
3. Optimize for the user's available time and learning style
4. Consider budget constraints and maximize free resources
5. Include practical application through projects
6. Build in flexibility for plan adjustments
7. Provide clear progress measurement methods
8. Include motivation and accountability strategies
9. Consider skill dependencies and logical progression
10. Integrate networking and community building

Create a plan that is realistic, actionable, and optimized for the user's success within their constraints.
`;

    const cacheKey = userId ? `personalized_learning_plan:${userId}:${userPreferences.timeframe}_${userPreferences.hoursPerWeek}h` : undefined;

    return this.generateContent(prompt, 'personalized_learning_plan', cacheKey, 45000, userId);
  }

  async analyzeSkillMarketTrends(
    skills: string[],
    targetIndustry: string,
    region: string = 'GLOBAL'
  ): Promise<AIResponse> {
    const prompt = `
Analyze current market trends and demand for the specified skills in JSON format:

SKILLS TO ANALYZE:
${skills.map(skill => `- ${skill}`).join('\n')}

TARGET INDUSTRY: ${targetIndustry}
REGION: ${region}

Please provide comprehensive market analysis in the following JSON structure:
{
  "marketAnalysis": {
    "analysisDate": "${new Date().toISOString()}",
    "region": "${region}",
    "industry": "${targetIndustry}",
    "overallMarketHealth": "EXCELLENT|GOOD|MODERATE|POOR|DECLINING"
  },
  "skillTrends": [
    {
      "skillName": "skill name",
      "demandLevel": "VERY_HIGH|HIGH|MODERATE|LOW|VERY_LOW",
      "growthTrend": "RAPIDLY_GROWING|GROWING|STABLE|DECLINING|RAPIDLY_DECLINING",
      "marketSaturation": "LOW|MODERATE|HIGH|OVERSATURATED",
      "averageSalaryImpact": number percentage,
      "jobPostingsGrowth": number percentage,
      "futureOutlook": {
        "nextYear": "trend prediction for next year",
        "nextFiveYears": "trend prediction for next 5 years",
        "emergingOpportunities": ["new opportunities emerging"],
        "potentialThreats": ["potential threats to skill relevance"]
      }
    }
  ]
}

ANALYSIS GUIDELINES:
1. Base analysis on current market trends and data
2. Consider industry-specific factors and requirements
3. Account for regional differences in demand
4. Factor in technological advancement impact
5. Consider economic factors affecting skill demand
6. Analyze both current state and future projections
7. Provide actionable insights for career planning
8. Consider skill interdependencies and combinations
9. Account for automation and AI impact on skills
10. Provide realistic timelines and expectations

Focus on providing accurate, actionable market intelligence that helps with strategic career planning and skill development decisions.
`;

    const cacheKey = `skill_market_trends:${targetIndustry}:${region}:${skills.join('_').replace(/\s+/g, '_')}`;

    return this.generateContent(prompt, 'skill_market_analysis', cacheKey, 30000);
  }

  async validateSkillAssessment(
    skillName: string,
    selfRating: number,
    userContext: {
      experienceLevel?: string;
      industry?: string;
      yearsOfExperience?: number;
      relatedSkills?: string[];
      previousAssessments?: Array<{
        skillName: string;
        rating: number;
        date: string;
      }>;
    }
  ): Promise<AIResponse> {
    const prompt = `
Validate and provide feedback on a skill self-assessment in JSON format:

SKILL ASSESSMENT:
- Skill: ${skillName}
- Self-Rating: ${selfRating}/10
- User Experience Level: ${userContext.experienceLevel || 'Not specified'}
- Industry: ${userContext.industry || 'Not specified'}
- Years of Experience: ${userContext.yearsOfExperience || 'Not specified'}
- Related Skills: ${userContext.relatedSkills?.join(', ') || 'None specified'}

PREVIOUS ASSESSMENTS:
${userContext.previousAssessments?.map(assessment =>
  `- ${assessment.skillName}: ${assessment.rating}/10 (${assessment.date})`
).join('\n') || 'No previous assessments'}

Please provide assessment validation in the following JSON structure:
{
  "validationResult": {
    "assessmentAccuracy": "ACCURATE|SLIGHTLY_HIGH|SLIGHTLY_LOW|SIGNIFICANTLY_HIGH|SIGNIFICANTLY_LOW",
    "confidenceLevel": number 0-100,
    "reasoning": "explanation of the validation assessment",
    "suggestedRating": number 1-10,
    "ratingJustification": "justification for suggested rating"
  },
  "skillAnalysis": {
    "skillComplexity": "BASIC|INTERMEDIATE|ADVANCED|EXPERT",
    "learningCurve": "GENTLE|MODERATE|STEEP|VERY_STEEP",
    "marketValue": "LOW|MODERATE|HIGH|VERY_HIGH",
    "industryRelevance": "LOW|MODERATE|HIGH|CRITICAL",
    "skillCategory": "TECHNICAL|SOFT_SKILL|DOMAIN_SPECIFIC|LEADERSHIP|CREATIVE"
  },
  "assessmentGuidance": {
    "ratingCriteria": {
      "level1to2": "criteria for beginner level (1-2)",
      "level3to4": "criteria for basic competency (3-4)",
      "level5to6": "criteria for intermediate level (5-6)",
      "level7to8": "criteria for advanced level (7-8)",
      "level9to10": "criteria for expert level (9-10)"
    },
    "selfAssessmentTips": [
      "tip for more accurate self-assessment",
      "another tip for better evaluation"
    ],
    "validationMethods": [
      {
        "method": "validation method name",
        "description": "how to validate this skill level",
        "timeRequired": "time needed for validation",
        "cost": "FREE|LOW|MODERATE|HIGH"
      }
    ]
  }
}

VALIDATION GUIDELINES:
1. Consider industry standards and typical skill progression
2. Account for experience level and career stage
3. Evaluate consistency with related skills
4. Provide constructive, actionable feedback
5. Consider market context and skill value
6. Suggest realistic improvement paths
7. Identify potential over/under-estimation
8. Provide objective validation methods
9. Consider skill complexity and learning curve
10. Offer benchmarking against industry standards

Focus on helping the user develop more accurate self-assessment skills while providing actionable guidance for skill development.
`;

    const cacheKey = `skill_assessment_validation:${skillName.replace(/\s+/g, '_')}:${selfRating}:${userContext.experienceLevel || 'unknown'}`;

    return this.generateContent(prompt, 'skill_assessment_validation', cacheKey, 30000);
  }

  async generateInterviewPrep(
    careerPath: string,
    experienceLevel: string,
    companyType: string,
    userId?: string
  ): Promise<AIResponse> {
    const prompt = `
Generate comprehensive interview preparation materials for the following scenario in JSON format:

Career Path: ${careerPath}
Experience Level: ${experienceLevel}
Company Type: ${companyType}

Please provide interview preparation in the following JSON structure:
{
  "commonQuestions": [
    {
      "question": "interview question",
      "category": "behavioral|technical|situational",
      "sampleAnswer": "example answer framework",
      "tips": ["specific tips for answering this question"]
    }
  ],
  "industrySpecificQuestions": [
    {
      "question": "industry-specific question",
      "difficulty": "easy|medium|hard",
      "keyPoints": ["key points to address in answer"]
    }
  ],
  "preparationTips": ["general interview preparation advice"]
}

Include:
1. 8-10 common interview questions with sample answers
2. 5-7 industry-specific technical questions
3. Behavioral questions using STAR method
4. Company research tips
5. Questions to ask the interviewer
`;

    const cacheKey = userId ? `interview_prep:${userId}:${careerPath.replace(/\s+/g, '_')}_${companyType}` : undefined;

    return this.generateContent(prompt, 'interview_prep', cacheKey, 30000, userId);
  }

  async generateInterviewQuestions(params: {
    sessionType: string;
    careerPath?: string;
    experienceLevel?: string;
    companyType?: string;
    industryFocus?: string;
    specificRole?: string;
    interviewType?: string;
    focusAreas?: any;
    difficulty?: string;
    questionTypes?: string[];
    categories?: string[];
    count: number;
  }): Promise<AIResponse> {
    // Basic validation for interview parameters
    if (!params.sessionType || typeof params.sessionType !== 'string') {
      AIServiceLogger.warn('Interview questions parameter validation failed - invalid sessionType', {
        params: params
      });
      return {
        success: false,
        error: 'Session type is required and must be a string'
      };
    }

    if (!params.count || params.count < 1 || params.count > 50) {
      AIServiceLogger.warn('Interview questions parameter validation failed - invalid count', {
        params: params
      });
      return {
        success: false,
        error: 'Question count must be between 1 and 50'
      };
    }
    const {
      sessionType,
      careerPath,
      experienceLevel,
      companyType,
      industryFocus,
      specificRole,
      interviewType,
      focusAreas,
      difficulty,
      questionTypes,
      categories,
      count
    } = params;

    const prompt = `
Generate ${count} interview questions for a practice session with the following parameters in JSON format:

Session Type: ${sessionType}
Career Path: ${careerPath || 'General'}
Experience Level: ${experienceLevel || 'INTERMEDIATE'}
Company Type: ${companyType || 'Corporate'}
Industry Focus: ${industryFocus || 'General'}
Specific Role: ${specificRole || 'Not specified'}
Interview Type: ${interviewType || 'VIDEO'}
Focus Areas: ${JSON.stringify(focusAreas) || 'General interview skills'}
Difficulty: ${difficulty || 'INTERMEDIATE'}
Question Types: ${JSON.stringify(questionTypes) || 'Mixed'}
Categories: ${JSON.stringify(categories) || 'Mixed'}

Please provide the questions in the following JSON structure:
{
  "questions": [
    {
      "questionText": "The actual interview question",
      "questionType": "BEHAVIORAL|TECHNICAL|SITUATIONAL|COMPANY_CULTURE|LEADERSHIP|PROBLEM_SOLVING|COMMUNICATION|STRESS_TEST|CASE_STUDY|ROLE_SPECIFIC",
      "category": "GENERAL|TECHNICAL_SKILLS|SOFT_SKILLS|LEADERSHIP|PROBLEM_SOLVING|COMMUNICATION|TEAMWORK|ADAPTABILITY|CREATIVITY|ANALYTICAL_THINKING|CUSTOMER_SERVICE|SALES|MANAGEMENT|STRATEGY|ETHICS|INDUSTRY_KNOWLEDGE",
      "difficulty": "BEGINNER|INTERMEDIATE|ADVANCED|EXPERT",
      "expectedDuration": 180,
      "context": "Why this question is asked and what interviewers look for",
      "hints": {
        "structure": "Suggested answer structure (e.g., STAR method)",
        "keyPoints": ["Key points to address"],
        "commonMistakes": ["What to avoid"]
      },
      "followUpQuestions": ["Potential follow-up questions"],
      "industrySpecific": false,
      "tags": ["relevant", "tags"],
      "isRequired": true
    }
  ],
  "metadata": {
    "sessionType": "${sessionType}",
    "totalQuestions": ${count},
    "difficultyDistribution": {"BEGINNER": 0, "INTERMEDIATE": 0, "ADVANCED": 0, "EXPERT": 0},
    "categoryDistribution": {"TECHNICAL_SKILLS": 0, "SOFT_SKILLS": 0, "LEADERSHIP": 0}
  }
}

Guidelines:
1. Ensure questions are appropriate for the experience level and role
2. Mix different question types and categories for comprehensive practice
3. Include both general and industry-specific questions when relevant
4. Provide helpful context and hints for each question
5. Make questions progressively challenging if multiple difficulty levels
6. Include behavioral questions that can use STAR method
7. Add technical questions relevant to the career path
8. Consider the company type when crafting questions
9. Ensure questions are realistic and commonly asked
10. Provide meaningful follow-up questions that interviewers might ask

Focus on creating high-quality, realistic interview questions that will genuinely help the user prepare for their target role and company type.
`;

    const cacheKey = `interview_questions:${sessionType}:${careerPath}:${difficulty}:${count}`;

    return this.generateContent(prompt, 'interview_questions', cacheKey, 30000);
  }

  async analyzeInterviewResponse(params: {
    questionText: string;
    questionType: string;
    questionCategory: string;
    responseText: string;
    responseTime: number;
    expectedDuration: number;
    careerPath?: string;
    experienceLevel?: string;
    context?: string;
  }): Promise<AIResponse> {
    // Validate response text
    const responseValidation = AIInputValidator.validateInterviewResponse(params.responseText);
    if (!responseValidation.isValid) {
      AIServiceLogger.warn('Interview response validation failed', {
        errors: responseValidation.errors
      });
      return {
        success: false,
        error: `Invalid response content: ${responseValidation.errors.join(', ')}`
      };
    }

    // Validate question text
    const questionValidation = AIInputValidator.validateTextInput(params.questionText, {
      maxLength: 1000,
      allowHtml: false,
      requireAlphanumeric: true
    });
    if (!questionValidation.isValid) {
      AIServiceLogger.warn('Interview question validation failed', {
        errors: questionValidation.errors
      });
      return {
        success: false,
        error: `Invalid question content: ${questionValidation.errors.join(', ')}`
      };
    }

    const sanitizedResponseText = responseValidation.sanitizedInput || params.responseText;
    const sanitizedQuestionText = questionValidation.sanitizedInput || params.questionText;
    const {
      questionText,
      questionType,
      questionCategory,
      responseText,
      responseTime,
      expectedDuration,
      careerPath,
      experienceLevel,
      context
    } = params;

    const prompt = `
Analyze the following interview response and provide comprehensive feedback in JSON format:

Question: ${sanitizedQuestionText}
Question Type: ${questionType}
Question Category: ${questionCategory}
Context: ${context || 'Standard interview question'}
Expected Duration: ${expectedDuration} seconds
Actual Response Time: ${responseTime} seconds
Career Path: ${careerPath || 'General'}
Experience Level: ${experienceLevel || 'INTERMEDIATE'}

Response:
${sanitizedResponseText}

Please provide your analysis in the following JSON structure:
{
  "overallScore": number between 1-10,
  "analysis": {
    "contentQuality": {
      "score": number between 1-10,
      "feedback": "Detailed feedback on content quality"
    },
    "structure": {
      "score": number between 1-10,
      "feedback": "Feedback on answer structure and organization"
    },
    "relevance": {
      "score": number between 1-10,
      "feedback": "How well the answer addresses the question"
    },
    "specificity": {
      "score": number between 1-10,
      "feedback": "Use of specific examples and details"
    },
    "timing": {
      "score": number between 1-10,
      "feedback": "Appropriateness of response length and timing"
    }
  },
  "feedback": {
    "positive": ["What the candidate did well"],
    "constructive": ["Areas for improvement with specific suggestions"],
    "missing": ["Key elements that should have been included"]
  },
  "strengths": ["Specific strengths demonstrated in the response"],
  "improvements": [
    {
      "area": "Area needing improvement",
      "suggestion": "Specific actionable suggestion",
      "priority": "high|medium|low"
    }
  ],
  "starMethodScore": number between 1-10 (for behavioral questions),
  "confidenceLevel": number between 1-10,
  "communicationScore": number between 1-10,
  "technicalScore": number between 1-10 (for technical questions)
}

Analysis Guidelines:
1. Be constructive and encouraging while providing honest feedback
2. Focus on specific, actionable improvements
3. Consider the experience level when evaluating the response
4. For behavioral questions, evaluate STAR method usage
5. For technical questions, assess accuracy and depth
6. Consider timing - too short may lack detail, too long may lose focus
7. Evaluate communication clarity and professionalism
8. Provide industry-specific feedback when relevant
9. Highlight both strengths and areas for growth
10. Offer specific examples of how to improve

Be thorough but supportive in your analysis to help the user improve their interview skills.
`;

    return this.generateContent(prompt, 'interview_analysis', undefined, 30000);
  }

  async generatePersonalizedContent(
    contentType: string,
    userContext: any,
    additionalParams: any = {},
    userId?: string
  ): Promise<AIResponse> {
    const prompt = `
Generate personalized ${contentType} content based on the following user context:

User Context:
${JSON.stringify(userContext, null, 2)}

Additional Parameters:
${JSON.stringify(additionalParams, null, 2)}

Please provide relevant, actionable, and personalized content that helps the user with their career transition goals.
Format the response as JSON with appropriate structure for the content type.
`;

    const cacheKey = userId ? `personalized_content:${userId}:${contentType}:${Date.now().toString().slice(-8)}` : undefined;

    return this.generateContent(prompt, 'career_recommendations', cacheKey, 30000, userId);
  }

  private generateRequestHash(prompt: string): string {
    // Generate a hash of the prompt for request deduplication
    const hash = Buffer.from(prompt).toString('base64').slice(0, 16);
    return hash;
  }

  private getRequestPriority(configKey: string): number {
    // Assign priority based on operation type
    const priorityMap: { [key: string]: number } = {
      'resume_analysis': 3,
      'career_recommendations': 2,
      'interview_questions': 2,
      'skills_analysis': 1,
      'interview_prep': 1
    };

    return priorityMap[configKey] || 1;
  }

  // Health check method
  async healthCheck(): Promise<{ ai: boolean; cache: { redis: boolean; memory: boolean } }> {
    let aiHealthy = false;

    try {
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('Health check timeout')), 10000);
      });

      const result = await Promise.race([
        this.model.generateContent({
          contents: [{ role: 'user', parts: [{ text: 'Hello, please respond with "OK"' }] }],
          generationConfig: { maxOutputTokens: 10 },
        }),
        timeoutPromise
      ]);

      const response = await result.response;
      aiHealthy = response.text().toLowerCase().includes('ok');
    } catch (error) {
      AIServiceLogger.warn('AI health check failed', error);
    }

    // Temporarily disabled advanced health checks
    // const cacheHealth = await redisCache.healthCheck();
    // const advancedCacheHealth = await advancedCacheManager.healthCheck();
    // const requestOptimizerHealth = await requestOptimizer.healthCheck();
    // const performanceMonitorHealth = await performanceMonitor.healthCheck();

    const cacheHealth = { redis: false, memory: true };
    const advancedCacheHealth = { status: 'disabled' };
    const requestOptimizerHealth = { status: 'disabled' };
    const performanceMonitorHealth = { status: 'disabled' };

    return {
      ai: aiHealthy,
      cache: cacheHealth
    };
  }

  // Get service statistics
  async getServiceStats() {
    // Temporarily disabled
    // const cacheStats = redisCache.getStats();
    const cacheStats = { redis: { connected: false }, memory: { size: 0 } };
    const rateLimiterStats = {
      activeUsers: this.rateLimiter.size,
      rateLimitPerMinute: this.RATE_LIMIT_REQUESTS_PER_MINUTE
    };

    return {
      cache: cacheStats,
      rateLimiter: rateLimiterStats,
      config: {
        cacheEnabled: this.cacheEnabled,
        cacheTTL: this.cacheTTL,
        model: 'gemini-1.5-flash'
      }
    };
  }

  // Enhanced security validation methods
  private containsHarmfulContent(text: string): boolean {
    const securityScan = AIInputValidator.securityScan(text);

    if (securityScan.riskLevel === 'high') {
      AIServiceLogger.error('High-risk content detected', { threats: securityScan.threats });
      return true;
    }

    if (securityScan.riskLevel === 'medium') {
      AIServiceLogger.warn('Medium-risk content detected', { threats: securityScan.threats });
      // Allow medium-risk content but log it
    }

    return false;
  }

  private validateResponseStructure(data: any, configKey: string): boolean {
    if (!data || typeof data !== 'object') {
      return false;
    }

    // Basic validation based on config type
    switch (configKey) {
      case 'resume_analysis':
        return this.validateResumeAnalysis(data);
      case 'career_recommendations':
        return this.validateCareerRecommendations(data);
      case 'interview_analysis':
        return this.validateInterviewAnalysis(data);
      case 'interview_questions':
        return this.validateInterviewQuestions(data);
      default:
        return true; // Allow unknown types for now
    }
  }

  private validateResumeAnalysis(data: any): boolean {
    return (
      Array.isArray(data.strengths) &&
      Array.isArray(data.weaknesses) &&
      Array.isArray(data.suggestions) &&
      typeof data.overallScore === 'number' &&
      data.overallScore >= 0 &&
      data.overallScore <= 100
    );
  }

  private validateCareerRecommendations(data: any): boolean {
    return (
      Array.isArray(data.recommendations) &&
      data.recommendations.every((rec: any) =>
        typeof rec.careerPath === 'string' &&
        typeof rec.matchScore === 'number' &&
        rec.matchScore >= 0 &&
        rec.matchScore <= 100
      )
    );
  }

  private validateInterviewAnalysis(data: any): boolean {
    return (
      typeof data.overallScore === 'number' &&
      data.overallScore >= 0 &&
      data.overallScore <= 10 &&
      typeof data.analysis === 'object' &&
      typeof data.feedback === 'object'
    );
  }

  private ensureInterviewAnalysisFields(data: any): any {
    // Ensure aiScore is present and valid
    if (typeof data.aiScore !== 'number' || isNaN(data.aiScore)) {
      // Try to extract from overallScore
      if (typeof data.overallScore === 'number' && !isNaN(data.overallScore)) {
        data.aiScore = data.overallScore;
      } else {
        // Fallback to a default score
        data.aiScore = 5;
        console.warn('AI response missing valid score, using fallback score of 5');
      }
    }

    // Ensure score is within valid range
    data.aiScore = Math.max(1, Math.min(10, data.aiScore));

    // Ensure other required fields exist
    if (!data.feedback) {
      data.feedback = { strengths: [], improvements: [] };
    }
    if (!data.strengths) {
      data.strengths = [];
    }
    if (!data.improvements) {
      data.improvements = [];
    }
    if (typeof data.starMethodScore !== 'number') {
      data.starMethodScore = data.aiScore;
    }
    if (typeof data.confidenceLevel !== 'number') {
      data.confidenceLevel = 0.8;
    }
    if (typeof data.communicationScore !== 'number') {
      data.communicationScore = data.aiScore;
    }

    return data;
  }

  private validateInterviewQuestions(data: any): boolean {
    return (
      Array.isArray(data.questions) &&
      data.questions.every((q: any) =>
        typeof q.questionText === 'string' &&
        typeof q.questionType === 'string' &&
        typeof q.category === 'string'
      )
    );
  }

  private sanitizeText(text: string): string {
    const validation = AIInputValidator.validateTextInput(text, {
      maxLength: 50000,
      allowHtml: false,
      allowSpecialChars: true
    });

    if (!validation.isValid) {
      AIServiceLogger.warn('Text sanitization failed', { errors: validation.errors });
      return '';
    }

    return validation.sanitizedInput || text;
  }
}

export const geminiService = new GeminiService();
export default geminiService;
