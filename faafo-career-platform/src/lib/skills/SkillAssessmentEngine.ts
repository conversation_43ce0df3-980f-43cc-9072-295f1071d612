import { v4 as uuidv4 } from 'uuid';
import { Edge<PERSON><PERSON><PERSON><PERSON><PERSON>, EdgeCaseResult, EdgeCaseOptions } from './EdgeCaseHandler';

export interface AssessmentConfig {
  questionsPerSkill?: number;
  difficultyLevels?: string[];
  timeLimit?: number;
  randomizeQuestions?: boolean;
  difficultyWeights?: Record<string, number>;
}

export interface AssessmentQuestion {
  id: string;
  skillId: string;
  question: string;
  options: string[];
  correctAnswer: number;
  difficulty: string;
  explanation?: string;
}

export interface AssessmentResponse {
  questionId: string;
  selectedAnswer: number;
  timeSpent: number;
  confidence?: number;
  isCorrect: boolean;
  timestamp: Date;
}

export interface Assessment {
  id: string;
  userId: string;
  careerPathId: string;
  skillIds: string[];
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled';
  config: AssessmentConfig;
  questions: AssessmentQuestion[];
  responses: AssessmentResponse[];
  createdAt: Date;
  updatedAt: Date;
  completedAt?: Date;
}

export interface AssessmentResults {
  assessmentId: string;
  skillScores: Record<string, number>;
  overallScore: number;
  averageConfidence?: number;
  averageTimePerQuestion: number;
  totalTimeSpent: number;
  completedAt: Date;
  recommendations: string[];
}

export interface CreateAssessmentParams {
  userId: string;
  careerPathId: string;
  skillIds: string[];
  config?: AssessmentConfig;
}

export interface RecordResponseParams {
  questionId: string;
  selectedAnswer: number;
  timeSpent: number;
  confidence?: number;
}

export class SkillAssessmentEngine {
  private assessments: Map<string, Assessment> = new Map();
  private edgeCaseHandler: EdgeCaseHandler;
  private defaultConfig: AssessmentConfig = {
    questionsPerSkill: 5,
    difficultyLevels: ['beginner', 'intermediate', 'advanced'],
    timeLimit: 1800, // 30 minutes
    randomizeQuestions: false,
    difficultyWeights: {
      beginner: 1,
      intermediate: 2,
      advanced: 3,
    },
  };

  constructor() {
    // EdgeCaseHandler will be initialized later when services are available
    this.edgeCaseHandler = null as any;
  }

  setEdgeCaseHandler(handler: EdgeCaseHandler) {
    this.edgeCaseHandler = handler;
  }

  createAssessment(params: CreateAssessmentParams): Assessment {
    if (!params.userId || !params.careerPathId || !params.skillIds.length) {
      throw new Error('Invalid assessment parameters');
    }

    const assessment: Assessment = {
      id: uuidv4(),
      userId: params.userId,
      careerPathId: params.careerPathId,
      skillIds: params.skillIds,
      status: 'pending',
      config: { ...this.defaultConfig, ...params.config },
      questions: [],
      responses: [],
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    this.assessments.set(assessment.id, assessment);
    return assessment;
  }

  /**
   * Create assessment with comprehensive edge case handling
   */
  async createAssessmentWithEdgeHandling(params: CreateAssessmentParams, options: EdgeCaseOptions = {}): Promise<EdgeCaseResult<Assessment>> {
    return this.edgeCaseHandler.handleSkillAssessment(params, options);
  }

  async generateQuestions(assessmentId: string): Promise<AssessmentQuestion[]> {
    const assessment = this.getAssessment(assessmentId);
    if (!assessment) {
      throw new Error('Assessment not found');
    }

    const questions: AssessmentQuestion[] = [];
    const questionsPerSkill = assessment.config.questionsPerSkill || 5;
    const difficultyLevels = assessment.config.difficultyLevels || ['beginner', 'intermediate', 'advanced'];

    for (const skillId of assessment.skillIds) {
      for (let i = 0; i < questionsPerSkill; i++) {
        const difficulty = difficultyLevels[i % difficultyLevels.length];
        const question = await this.generateQuestionForSkill(skillId, difficulty);
        questions.push(question);
      }
    }

    if (assessment.config.randomizeQuestions) {
      this.shuffleArray(questions);
    }

    assessment.questions = questions;
    assessment.updatedAt = new Date();
    this.assessments.set(assessmentId, assessment);

    return questions;
  }

  async recordResponse(assessmentId: string, params: RecordResponseParams): Promise<AssessmentResponse> {
    const assessment = this.getAssessment(assessmentId);
    if (!assessment) {
      throw new Error('Assessment not found');
    }

    if (assessment.status === 'completed') {
      throw new Error('Cannot modify completed assessment');
    }

    const question = assessment.questions.find(q => q.id === params.questionId);
    if (!question) {
      throw new Error('Invalid question ID');
    }

    if (params.selectedAnswer < 0 || params.selectedAnswer >= question.options.length) {
      throw new Error('Invalid answer selection');
    }

    const existingResponse = assessment.responses.find(r => r.questionId === params.questionId);
    if (existingResponse) {
      throw new Error('Question already answered');
    }

    const response: AssessmentResponse = {
      questionId: params.questionId,
      selectedAnswer: params.selectedAnswer,
      timeSpent: params.timeSpent,
      confidence: params.confidence,
      isCorrect: params.selectedAnswer === question.correctAnswer,
      timestamp: new Date(),
    };

    assessment.responses.push(response);
    assessment.updatedAt = new Date();

    if (assessment.status === 'pending') {
      assessment.status = 'in_progress';
    }

    this.assessments.set(assessmentId, assessment);
    return response;
  }

  /**
   * Record response with comprehensive edge case handling
   */
  async recordResponseWithEdgeHandling(assessmentId: string, params: RecordResponseParams, options: EdgeCaseOptions = {}): Promise<EdgeCaseResult<AssessmentResponse>> {
    try {
      const response = await this.recordResponse(assessmentId, params);
      return {
        success: true,
        data: response,
        sanitizedInput: params
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        errorType: 'SYSTEM_ERROR',
        retryable: true,
        fallbackData: null
      };
    }
  }

  async calculateResults(assessmentId: string): Promise<AssessmentResults> {
    const assessment = this.getAssessment(assessmentId);
    if (!assessment) {
      throw new Error('Assessment not found');
    }

    const skillScores: Record<string, number> = {};
    const difficultyWeights = assessment.config.difficultyWeights || this.defaultConfig.difficultyWeights!;

    // Calculate scores for each skill
    for (const skillId of assessment.skillIds) {
      const skillQuestions = assessment.questions.filter(q => q.skillId === skillId);
      const skillResponses = assessment.responses.filter(r => 
        skillQuestions.some(q => q.id === r.questionId)
      );

      let totalWeight = 0;
      let weightedScore = 0;

      for (const response of skillResponses) {
        const question = skillQuestions.find(q => q.id === response.questionId)!;
        const weight = difficultyWeights[question.difficulty] || 1;
        totalWeight += weight;
        if (response.isCorrect) {
          weightedScore += weight;
        }
      }

      skillScores[skillId] = totalWeight > 0 ? (weightedScore / totalWeight) * 100 : 0;
    }

    // Calculate overall score
    const overallScore = Object.values(skillScores).reduce((sum, score) => sum + score, 0) / Object.keys(skillScores).length;

    // Calculate confidence and time metrics
    const confidenceValues = assessment.responses.filter(r => r.confidence !== undefined).map(r => r.confidence!);
    const averageConfidence = confidenceValues.length > 0 
      ? confidenceValues.reduce((sum, conf) => sum + conf, 0) / confidenceValues.length 
      : undefined;

    const totalTimeSpent = assessment.responses.reduce((sum, r) => sum + r.timeSpent, 0);
    const averageTimePerQuestion = assessment.responses.length > 0 
      ? totalTimeSpent / assessment.responses.length 
      : 0;

    const results: AssessmentResults = {
      assessmentId,
      skillScores,
      overallScore,
      averageConfidence,
      averageTimePerQuestion,
      totalTimeSpent,
      completedAt: new Date(),
      recommendations: this.generateRecommendations(skillScores, overallScore),
    };

    // Update assessment status
    assessment.status = 'completed';
    assessment.completedAt = results.completedAt;
    assessment.updatedAt = new Date();
    this.assessments.set(assessmentId, assessment);

    return results;
  }

  getAssessment(assessmentId: string): Assessment | null {
    return this.assessments.get(assessmentId) || null;
  }

  getAssessmentsByUser(userId: string): Assessment[] {
    return Array.from(this.assessments.values()).filter(a => a.userId === userId);
  }

  // Debug method for testing
  getAllAssessments(): Assessment[] {
    return Array.from(this.assessments.values());
  }

  async updateAssessmentStatus(assessmentId: string, status: Assessment['status']): Promise<void> {
    const assessment = this.getAssessment(assessmentId);
    if (!assessment) {
      throw new Error('Assessment not found');
    }

    assessment.status = status;
    assessment.updatedAt = new Date();
    this.assessments.set(assessmentId, assessment);
  }

  private async generateQuestionForSkill(skillId: string, difficulty: string): Promise<AssessmentQuestion> {
    // This is a simplified implementation. In a real system, this would
    // fetch questions from a database or generate them using AI
    const questionTemplates = this.getQuestionTemplates(skillId, difficulty);
    const template = questionTemplates[Math.floor(Math.random() * questionTemplates.length)];

    return {
      id: `${uuidv4()}-${skillId}-${difficulty}-${Date.now()}-${Math.random()}`,
      skillId,
      question: template.question,
      options: template.options,
      correctAnswer: template.correctAnswer,
      difficulty,
      explanation: template.explanation,
    };
  }

  private getQuestionTemplates(skillId: string, difficulty: string) {
    // Simplified question templates for testing
    const templates = {
      javascript: {
        beginner: [
          {
            question: "What is the correct way to declare a variable in JavaScript?",
            options: ["var x = 5;", "variable x = 5;", "v x = 5;", "declare x = 5;"],
            correctAnswer: 0,
            explanation: "The 'var' keyword is used to declare variables in JavaScript."
          },
          {
            question: "Which of the following is a JavaScript data type?",
            options: ["string", "boolean", "number", "all of the above"],
            correctAnswer: 3,
            explanation: "JavaScript has several primitive data types including string, boolean, and number."
          }
        ],
        intermediate: [
          {
            question: "What does the 'this' keyword refer to in JavaScript?",
            options: ["The current function", "The global object", "The object that owns the method", "The parent object"],
            correctAnswer: 2,
            explanation: "'this' refers to the object that owns the method being executed."
          }
        ],
        advanced: [
          {
            question: "What is a closure in JavaScript?",
            options: ["A function inside another function", "A way to close a program", "A function that has access to outer scope", "A type of loop"],
            correctAnswer: 2,
            explanation: "A closure is a function that has access to variables in its outer (enclosing) scope even after the outer function has returned."
          }
        ]
      },
      react: {
        beginner: [
          {
            question: "What is JSX?",
            options: ["A JavaScript library", "A syntax extension for JavaScript", "A CSS framework", "A database"],
            correctAnswer: 1,
            explanation: "JSX is a syntax extension for JavaScript that allows you to write HTML-like code in your JavaScript files."
          }
        ],
        intermediate: [
          {
            question: "What is the purpose of useState hook?",
            options: ["To manage component state", "To handle side effects", "To optimize performance", "To create components"],
            correctAnswer: 0,
            explanation: "useState is a React hook that allows you to add state to functional components."
          }
        ],
        advanced: [
          {
            question: "What is the difference between useEffect and useLayoutEffect?",
            options: ["No difference", "useLayoutEffect runs synchronously", "useEffect runs synchronously", "useLayoutEffect is deprecated"],
            correctAnswer: 1,
            explanation: "useLayoutEffect runs synchronously after all DOM mutations but before the browser paints."
          }
        ]
      },
      nodejs: {
        beginner: [
          {
            question: "What is Node.js?",
            options: ["A web browser", "A JavaScript runtime", "A database", "A CSS framework"],
            correctAnswer: 1,
            explanation: "Node.js is a JavaScript runtime built on Chrome's V8 JavaScript engine."
          }
        ],
        intermediate: [
          {
            question: "What is npm?",
            options: ["Node Package Manager", "New Programming Method", "Network Protocol Manager", "Node Process Manager"],
            correctAnswer: 0,
            explanation: "npm stands for Node Package Manager and is the default package manager for Node.js."
          }
        ],
        advanced: [
          {
            question: "What is the event loop in Node.js?",
            options: ["A loop that handles events", "A mechanism for non-blocking I/O", "A way to create loops", "A debugging tool"],
            correctAnswer: 1,
            explanation: "The event loop is what allows Node.js to perform non-blocking I/O operations despite JavaScript being single-threaded."
          }
        ]
      }
    };

    const skillTemplates = templates[skillId as keyof typeof templates];
    if (!skillTemplates) {
      return [{
        question: `Sample ${difficulty} question for ${skillId}`,
        options: ["Option A", "Option B", "Option C", "Option D"],
        correctAnswer: 0,
        explanation: "This is a sample question."
      }];
    }

    return skillTemplates[difficulty as keyof typeof skillTemplates] || skillTemplates.beginner;
  }

  private shuffleArray<T>(array: T[]): void {
    for (let i = array.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [array[i], array[j]] = [array[j], array[i]];
    }
  }

  private generateRecommendations(skillScores: Record<string, number>, overallScore: number): string[] {
    const recommendations: string[] = [];

    if (overallScore < 50) {
      recommendations.push("Consider focusing on fundamental concepts before advancing to more complex topics.");
    } else if (overallScore < 75) {
      recommendations.push("Good foundation! Focus on practicing more advanced concepts.");
    } else {
      recommendations.push("Excellent performance! You're ready for advanced challenges.");
    }

    // Add skill-specific recommendations
    for (const [skill, score] of Object.entries(skillScores)) {
      if (score < 60) {
        recommendations.push(`Consider additional practice with ${skill} fundamentals.`);
      } else if (score > 85) {
        recommendations.push(`Strong ${skill} skills! Consider mentoring others or taking on leadership roles.`);
      }
    }

    return recommendations;
  }
}
