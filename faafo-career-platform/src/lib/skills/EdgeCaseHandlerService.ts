import { EdgeC<PERSON><PERSON>and<PERSON>, EdgeCaseResult, EdgeCaseOptions } from './EdgeCaseHandler';
import { SkillAssessmentEngine } from './SkillAssessmentEngine';
import { SkillMarketDataService } from './SkillMarketDataService';
import { PersonalizedLearningPathService } from './PersonalizedLearningPathService';
import { skillServiceFactory } from './ServiceFactory';
import { prisma } from '@/lib/prisma';
import { geminiService } from '@/lib/services/geminiService';
import { skillGapPerformanceOptimizer } from '@/lib/performance/SkillGapPerformanceOptimizer';

/**
 * Service factory for creating EdgeCaseHandler with proper dependencies
 */
export class EdgeCaseHandlerService {
  private static instance: EdgeCaseHandlerService;
  private edgeCaseHandler: EdgeCaseHandler;
  private assessmentEngine: SkillAssessmentEngine;
  private marketDataService: SkillMarketDataService;
  private learningPathService: PersonalizedLearningPathService;

  private constructor() {
    // Get properly integrated services from factory
    const services = skillServiceFactory.getServices();
    this.edgeCaseHandler = services.edgeCaseHandler;
    this.assessmentEngine = services.assessmentEngine;
    this.marketDataService = services.marketDataService;
    this.learningPathService = services.learningPathService;
  }

  /**
   * Get singleton instance of EdgeCaseHandlerService
   */
  public static getInstance(): EdgeCaseHandlerService {
    if (!EdgeCaseHandlerService.instance) {
      EdgeCaseHandlerService.instance = new EdgeCaseHandlerService();
    }
    return EdgeCaseHandlerService.instance;
  }

  /**
   * Handle skill assessment with comprehensive error handling
   */
  async handleSkillAssessment(request: any, options: EdgeCaseOptions = {}): Promise<EdgeCaseResult> {
    return this.edgeCaseHandler.handleSkillAssessment(request, options);
  }

  /**
   * Handle learning path generation with comprehensive error handling
   */
  async handleLearningPathGeneration(request: any, options: EdgeCaseOptions = {}): Promise<EdgeCaseResult> {
    return this.edgeCaseHandler.handleLearningPathGeneration(request, options);
  }

  /**
   * Handle market data retrieval with comprehensive error handling
   */
  async handleMarketDataRetrieval(request: any, options: EdgeCaseOptions = {}): Promise<EdgeCaseResult> {
    return this.edgeCaseHandler.handleMarketDataRequest(request, options);
  }

  /**
   * Handle user data retrieval with comprehensive error handling
   */
  async handleUserDataRetrieval(request: any, options: EdgeCaseOptions = {}): Promise<EdgeCaseResult> {
    try {
      const { userId, dataType = 'assessments' } = request;

      if (!userId) {
        return {
          success: false,
          error: 'User ID is required',
          errorType: 'VALIDATION_ERROR',
          fallbackData: null
        };
      }

      // Get user data based on type
      let data;
      switch (dataType) {
        case 'assessments':
        case 'skill_assessments':
          data = await prisma.skillAssessment.findMany({
            where: { userId, isActive: true },
            include: {
              skill: true
            },
            orderBy: { assessmentDate: 'desc' }
          });
          break;
        case 'learningPaths':
          data = await prisma.userLearningPath.findMany({
            where: { userId },
            include: { learningPath: true },
            orderBy: { createdAt: 'desc' }
          });
          break;
        default:
          data = await prisma.skillAssessment.findMany({
            where: { userId, isActive: true },
            include: {
              skill: true
            },
            orderBy: { assessmentDate: 'desc' }
          });
      }

      return {
        success: true,
        data,
        fallbackData: null
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        errorType: 'SYSTEM_ERROR',
        fallbackData: []
      };
    }
  }

  /**
   * Get error statistics from EdgeCaseHandler
   */
  async getErrorStatistics() {
    return this.edgeCaseHandler.getErrorStatistics();
  }

  /**
   * Get health status from EdgeCaseHandler
   */
  async getHealthStatus() {
    return this.edgeCaseHandler.getHealthStatus();
  }

  /**
   * Get performance metrics and cache statistics
   */
  getPerformanceMetrics() {
    return {
      metrics: skillGapPerformanceOptimizer.getPerformanceMetrics(),
      cacheStats: skillGapPerformanceOptimizer.getCacheStats(),
    };
  }

  /**
   * Clear all performance caches
   */
  clearPerformanceCaches() {
    skillGapPerformanceOptimizer.clearCaches();
  }

  /**
   * Enhanced skill assessment that integrates with database and AI services
   */
  async createSkillAssessmentWithDatabase(params: {
    userId: string;
    skillIds: string[];
    careerPathId?: string;
    assessmentType?: string;
  }): Promise<EdgeCaseResult> {
    try {
      // Validate user exists (with performance optimization)
      let user = await skillGapPerformanceOptimizer.getUser(params.userId);

      // No automatic user creation in development - <EMAIL> has been eliminated
      if (!user) {
        console.log('User not found and automatic creation disabled:', params.userId);
        return {
          success: false,
          error: 'User not found - authentication required',
          errorType: 'AUTHENTICATION_ERROR' as const,
          retryable: false
        };
      }

      // Validate skills exist (with batch optimization)
      const skills = await skillGapPerformanceOptimizer.getSkills(params.skillIds);

      if (skills.length !== params.skillIds.length) {
        const foundSkillIds = skills.map(s => s.id);
        const missingSkillIds = params.skillIds.filter(id => !foundSkillIds.includes(id));

        return {
          success: false,
          error: `Skills not found: ${missingSkillIds.join(', ')}`,
          errorType: 'BUSINESS_LOGIC_ERROR',
          suggestedAlternatives: await this.getSuggestedSkillAlternatives(missingSkillIds)
        };
      }

      // Use EdgeCaseHandler for comprehensive error handling
      const result = await this.handleSkillAssessment({
        userId: params.userId,
        skillIds: params.skillIds,
        careerPathId: params.careerPathId,
        assessmentType: params.assessmentType || 'comprehensive'
      });

      // If successful, save to database using upsert to handle unique constraints
      if (result.success && result.data) {
        const assessment = await prisma.skillAssessment.upsert({
          where: {
            userId_skillId_assessmentType: {
              userId: params.userId,
              skillId: params.skillIds[0], // For now, handle single skill
              assessmentType: (params.assessmentType as any) || 'SELF_ASSESSMENT',
            },
          },
          update: {
            selfRating: result.data.overallScore || 5,
            confidenceLevel: result.data.averageConfidence || 5,
            assessmentDate: new Date(),
            notes: `EdgeCaseHandler assessment - ID: ${result.data.id}, Skills: ${params.skillIds.join(', ')}, Career Path: ${params.careerPathId}`
          },
          create: {
            userId: params.userId,
            skillId: params.skillIds[0], // For now, handle single skill
            selfRating: result.data.overallScore || 5,
            confidenceLevel: result.data.averageConfidence || 5,
            assessmentType: (params.assessmentType as any) || 'SELF_ASSESSMENT',
            assessmentDate: new Date(),
            notes: `EdgeCaseHandler assessment - ID: ${result.data.id}, Skills: ${params.skillIds.join(', ')}, Career Path: ${params.careerPathId}`
          }
        });

        result.data.databaseId = assessment.id;
      }

      return result;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        errorType: 'SYSTEM_ERROR',
        fallbackData: null
      };
    }
  }

  /**
   * Enhanced learning path generation that integrates with database and AI services
   */
  async generateLearningPathWithDatabase(params: {
    userId: string;
    targetRole: string;
    currentSkills: Array<{ skill: string; level: number }>;
    timeframe?: number;
    budget?: number;
    availability?: number;
  }): Promise<EdgeCaseResult> {
    try {
      // Validate user exists (with performance optimization)
      const user = await skillGapPerformanceOptimizer.getUser(params.userId);

      if (!user) {
        return {
          success: false,
          error: 'User not found',
          errorType: 'VALIDATION_ERROR',
          fallbackData: null
        };
      }

      // Get career path from database
      const careerPath = await prisma.careerPath.findFirst({
        where: {
          name: {
            contains: params.targetRole,
            mode: 'insensitive'
          }
        },
        include: {
          relatedSkills: true
        }
      });

      // Use EdgeCaseHandler for comprehensive error handling
      const result = await this.handleLearningPathGeneration({
        userId: params.userId,
        targetRole: params.targetRole,
        currentSkills: params.currentSkills,
        timeframe: params.timeframe,
        budget: params.budget,
        availability: params.availability,
        careerPathData: careerPath
      });

      // If successful, save to database
      if (result.success && result.data) {
        const learningPath = await prisma.learningPath.create({
          data: {
            title: `Learning Path for ${params.targetRole}`,
            description: `Personalized learning path to become a ${params.targetRole}`,
            slug: `learning-path-${params.targetRole.toLowerCase().replace(/\s+/g, '-')}-${Date.now()}`,
            difficulty: 'INTERMEDIATE',
            estimatedHours: result.data.estimatedDuration || 12,
            category: 'WEB_DEVELOPMENT', // Default category
            createdBy: params.userId,
            isActive: true
          }
        });

        result.data.databaseId = learningPath.id;
      }

      return result;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        errorType: 'SYSTEM_ERROR',
        fallbackData: null
      };
    }
  }

  /**
   * Enhanced market data retrieval that integrates with database
   */
  async getMarketDataWithDatabase(params: {
    skill: string;
    location?: string;
    forceRefresh?: boolean;
  }): Promise<EdgeCaseResult> {
    try {
      // Use performance-optimized market data retrieval
      const marketData = await skillGapPerformanceOptimizer.getMarketData(
        params.skill,
        params.location
      );

      // Use EdgeCaseHandler for comprehensive error handling
      const result = await this.handleMarketDataRetrieval({
        skill: params.skill,
        location: params.location,
        forceRefresh: params.forceRefresh,
        existingData: marketData
      });

      // If successful and we have new data, save to database
      if (result.success && result.data) {
        const skill = await skillGapPerformanceOptimizer.getSkillByName(params.skill);
        if (skill) {
          await prisma.skillMarketData.create({
            data: {
              skillId: skill.id,
              region: params.location || 'GLOBAL',
              demandLevel: result.data.demand > 70 ? 'VERY_HIGH' :
                          result.data.demand > 50 ? 'HIGH' :
                          result.data.demand > 30 ? 'MODERATE' : 'LOW',
              averageSalaryImpact: result.data.averageSalary ? (result.data.averageSalary / 75000) * 100 : null,
              jobPostingsCount: result.data.jobPostings || 0,
              growthTrend: result.data.growth > 10 ? 'RAPIDLY_GROWING' :
                          result.data.growth > 5 ? 'GROWING' :
                          result.data.growth > 0 ? 'STABLE' : 'DECLINING',
              industryRelevance: result.data.industries || ['Technology'],
              dataSource: 'EdgeCaseHandler',
              dataDate: new Date(),
              isActive: true
            }
          });
        }
      }

      return result;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        errorType: 'SYSTEM_ERROR',
        fallbackData: null
      };
    }
  }

  /**
   * Get suggested skill alternatives for missing skills
   */
  private async getSuggestedSkillAlternatives(missingSkillIds: string[]): Promise<string[]> {
    try {
      // Get similar skills from database
      const similarSkills = await prisma.skill.findMany({
        where: {
          OR: missingSkillIds.map(skillId => ({
            name: {
              contains: skillId,
              mode: 'insensitive'
            }
          }))
        },
        take: 5
      });

      return similarSkills.map(skill => skill.name);
    } catch (error) {
      // Return default alternatives
      return ['JavaScript', 'React', 'Node.js', 'Python', 'TypeScript'];
    }
  }
}

// Export singleton instance
export const edgeCaseHandlerService = EdgeCaseHandlerService.getInstance();
