/**
 * React hook for comprehensive form validation
 * Provides real-time validation, error handling, and sanitization
 */

import { useState, useCallback, useEffect } from 'react';
import { ClientSideValidator, ValidationRule, ValidationResult } from '@/lib/client-validation';

export interface UseFormValidationOptions {
  rules: Record<string, ValidationRule>;
  validateOnChange?: boolean;
  validateOnBlur?: boolean;
  debounceMs?: number;
  onValidationChange?: (isValid: boolean, errors: Record<string, string>) => void;
}

export interface FormValidationState {
  errors: Record<string, string>;
  warnings: string[];
  isValid: boolean;
  isValidating: boolean;
  touchedFields: Set<string>;
}

export interface FormValidationActions {
  validateField: (fieldName: string, value: any) => Promise<void>;
  validateForm: (data: Record<string, any>) => ValidationResult;
  clearErrors: (fieldNames?: string[]) => void;
  clearAllErrors: () => void;
  markFieldTouched: (fieldName: string) => void;
  resetValidation: () => void;
  getFieldError: (fieldName: string) => string | undefined;
  isFieldValid: (fieldName: string) => boolean;
  hasFieldError: (fieldName: string) => boolean;
}

export function useFormValidation(options: UseFormValidationOptions): [FormValidationState, FormValidationActions] {
  const {
    rules,
    validateOnChange = true,
    validateOnBlur = true,
    debounceMs = 300,
    onValidationChange
  } = options;

  const [state, setState] = useState<FormValidationState>({
    errors: {},
    warnings: [],
    isValid: true,
    isValidating: false,
    touchedFields: new Set()
  });

  // Debounced validation timers
  const [validationTimers, setValidationTimers] = useState<Record<string, NodeJS.Timeout>>({});

  // Clear timers on unmount
  useEffect(() => {
    return () => {
      Object.values(validationTimers).forEach(timer => clearTimeout(timer));
    };
  }, [validationTimers]);

  // Notify parent component of validation changes
  useEffect(() => {
    if (onValidationChange) {
      onValidationChange(state.isValid, state.errors);
    }
  }, [state.isValid, state.errors, onValidationChange]);

  const validateField = useCallback(async (fieldName: string, value: any): Promise<void> => {
    const fieldRules = rules[fieldName];
    if (!fieldRules) return;

    // Clear existing timer for this field
    if (validationTimers[fieldName]) {
      clearTimeout(validationTimers[fieldName]);
    }

    setState(prev => ({ ...prev, isValidating: true }));

    // Set new timer for debounced validation
    const timer = setTimeout(async () => {
      const result = ClientSideValidator.validateField(fieldName, value, fieldRules);

      setState(prev => {
        const newErrors = { ...prev.errors };
        const newWarnings = [...prev.warnings];

        // Update field error
        if (result.error) {
          newErrors[fieldName] = result.error;
        } else {
          delete newErrors[fieldName];
        }

        // Add warning if present
        if (result.warning && !newWarnings.includes(result.warning)) {
          newWarnings.push(result.warning);
        }

        const isValid = Object.keys(newErrors).length === 0;

        return {
          ...prev,
          errors: newErrors,
          warnings: newWarnings,
          isValid,
          isValidating: false
        };
      });

      // Clean up timer
      setValidationTimers(prev => {
        const newTimers = { ...prev };
        delete newTimers[fieldName];
        return newTimers;
      });
    }, debounceMs);

    setValidationTimers(prev => ({ ...prev, [fieldName]: timer }));
  }, [rules, debounceMs, validationTimers]);

  const validateForm = useCallback((data: Record<string, any>): ValidationResult => {
    setState(prev => ({ ...prev, isValidating: true }));

    const result = ClientSideValidator.validateForm(data, rules);

    setState(prev => ({
      ...prev,
      errors: result.errors,
      warnings: result.warnings,
      isValid: result.isValid,
      isValidating: false
    }));

    return result;
  }, [rules]);

  const clearErrors = useCallback((fieldNames?: string[]) => {
    setState(prev => {
      const newErrors = { ...prev.errors };
      
      if (fieldNames) {
        fieldNames.forEach(fieldName => {
          delete newErrors[fieldName];
        });
      } else {
        // Clear all errors
        Object.keys(newErrors).forEach(key => {
          delete newErrors[key];
        });
      }

      const isValid = Object.keys(newErrors).length === 0;

      return {
        ...prev,
        errors: newErrors,
        isValid
      };
    });
  }, []);

  const clearAllErrors = useCallback(() => {
    setState(prev => ({
      ...prev,
      errors: {},
      warnings: [],
      isValid: true
    }));
  }, []);

  const markFieldTouched = useCallback((fieldName: string) => {
    setState(prev => ({
      ...prev,
      touchedFields: new Set(Array.from(prev.touchedFields).concat(fieldName))
    }));
  }, []);

  const resetValidation = useCallback(() => {
    // Clear all timers
    Object.values(validationTimers).forEach(timer => clearTimeout(timer));
    setValidationTimers({});

    setState({
      errors: {},
      warnings: [],
      isValid: true,
      isValidating: false,
      touchedFields: new Set()
    });
  }, [validationTimers]);

  const getFieldError = useCallback((fieldName: string): string | undefined => {
    return state.errors[fieldName];
  }, [state.errors]);

  const isFieldValid = useCallback((fieldName: string): boolean => {
    return !state.errors[fieldName];
  }, [state.errors]);

  const hasFieldError = useCallback((fieldName: string): boolean => {
    return !!state.errors[fieldName];
  }, [state.errors]);

  const actions: FormValidationActions = {
    validateField,
    validateForm,
    clearErrors,
    clearAllErrors,
    markFieldTouched,
    resetValidation,
    getFieldError,
    isFieldValid,
    hasFieldError
  };

  return [state, actions];
}

/**
 * Hook for validating a single field with real-time feedback
 */
export function useFieldValidation(
  fieldName: string,
  rules: ValidationRule,
  debounceMs: number = 300
) {
  const [error, setError] = useState<string | undefined>();
  const [warning, setWarning] = useState<string | undefined>();
  const [isValidating, setIsValidating] = useState(false);
  const [timer, setTimer] = useState<NodeJS.Timeout | null>(null);

  const validate = useCallback(async (value: any) => {
    // Clear existing timer
    if (timer) {
      clearTimeout(timer);
    }

    setIsValidating(true);

    // Set new timer for debounced validation
    const newTimer = setTimeout(async () => {
      const result = ClientSideValidator.validateField(fieldName, value, rules);
      
      setError(result.error);
      setWarning(result.warning);
      setIsValidating(false);
      setTimer(null);
    }, debounceMs);

    setTimer(newTimer);
  }, [fieldName, rules, debounceMs, timer]);

  const clearValidation = useCallback(() => {
    if (timer) {
      clearTimeout(timer);
      setTimer(null);
    }
    setError(undefined);
    setWarning(undefined);
    setIsValidating(false);
  }, [timer]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (timer) {
        clearTimeout(timer);
      }
    };
  }, [timer]);

  return {
    error,
    warning,
    isValidating,
    isValid: !error,
    validate,
    clearValidation
  };
}

/**
 * Hook for form submission with validation
 */
export function useValidatedForm<T extends Record<string, any>>(
  initialData: T,
  rules: Record<string, ValidationRule>,
  onSubmit: (data: T, sanitizedData: Record<string, any>) => Promise<void> | void
) {
  const [data, setData] = useState<T>(initialData);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState<string | undefined>();

  const [validationState, validationActions] = useFormValidation({
    rules,
    validateOnChange: true,
    validateOnBlur: true
  });

  const updateField = useCallback((fieldName: keyof T, value: any) => {
    setData(prev => ({ ...prev, [fieldName]: value }));
    validationActions.validateField(fieldName as string, value);
    validationActions.markFieldTouched(fieldName as string);
  }, [validationActions]);

  const handleSubmit = useCallback(async (e?: React.FormEvent) => {
    if (e) {
      e.preventDefault();
    }

    setSubmitError(undefined);
    setIsSubmitting(true);

    try {
      // Validate entire form
      const validationResult = validationActions.validateForm(data);

      if (!validationResult.isValid) {
        setIsSubmitting(false);
        return;
      }

      // Submit with sanitized data
      await onSubmit(data, validationResult.sanitizedData || {});
    } catch (error) {
      setSubmitError(error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : String(error) : String(error) : 'An error occurred');
    } finally {
      setIsSubmitting(false);
    }
  }, [data, validationActions, onSubmit]);

  const resetForm = useCallback(() => {
    setData(initialData);
    setSubmitError(undefined);
    setIsSubmitting(false);
    validationActions.resetValidation();
  }, [initialData, validationActions]);

  return {
    data,
    updateField,
    handleSubmit,
    resetForm,
    isSubmitting,
    submitError,
    validation: validationState,
    validationActions
  };
}
