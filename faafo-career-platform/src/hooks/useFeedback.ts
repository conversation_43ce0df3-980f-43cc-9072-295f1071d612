'use client';

import { useState, useCallback } from 'react';
import { FeedbackMessage, FeedbackType, FeedbackAction } from '@/components/ui/error-feedback';

export interface UseFeedbackReturn {
  messages: FeedbackMessage[];
  showFeedback: (message: Omit<FeedbackMessage, 'id'>) => string;
  showSuccess: (message: string, options?: Partial<FeedbackMessage>) => string;
  showError: (message: string, options?: Partial<FeedbackMessage>) => string;
  showWarning: (message: string, options?: Partial<FeedbackMessage>) => string;
  showInfo: (message: string, options?: Partial<FeedbackMessage>) => string;
  showLoading: (message: string, options?: Partial<FeedbackMessage>) => string;
  dismissFeedback: (id: string) => void;
  clearAll: () => void;
  updateFeedback: (id: string, updates: Partial<FeedbackMessage>) => void;
}

export function useFeedback(): UseFeedbackReturn {
  const [messages, setMessages] = useState<FeedbackMessage[]>([]);

  const generateId = useCallback(() => {
    return `feedback-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }, []);

  const showFeedback = useCallback((message: Omit<FeedbackMessage, 'id'>) => {
    const id = generateId();
    const newMessage: FeedbackMessage = {
      ...message,
      id,
      dismissible: message.dismissible ?? true,
      autoHide: message.autoHide ?? (message.type === 'success' || message.type === 'info'),
      duration: message.duration ?? (message.type === 'success' ? 5000 : message.type === 'info' ? 7000 : undefined)
    };

    setMessages(prev => [...prev, newMessage]);
    return id;
  }, [generateId]);

  const showSuccess = useCallback((message: string, options: Partial<FeedbackMessage> = {}) => {
    return showFeedback({
      type: 'success',
      message,
      ...options
    });
  }, [showFeedback]);

  const showError = useCallback((message: string, options: Partial<FeedbackMessage> = {}) => {
    return showFeedback({
      type: 'error',
      message,
      autoHide: false, // Errors should not auto-hide by default
      ...options
    });
  }, [showFeedback]);

  const showWarning = useCallback((message: string, options: Partial<FeedbackMessage> = {}) => {
    return showFeedback({
      type: 'warning',
      message,
      autoHide: false, // Warnings should not auto-hide by default
      ...options
    });
  }, [showFeedback]);

  const showInfo = useCallback((message: string, options: Partial<FeedbackMessage> = {}) => {
    return showFeedback({
      type: 'info',
      message,
      ...options
    });
  }, [showFeedback]);

  const showLoading = useCallback((message: string, options: Partial<FeedbackMessage> = {}) => {
    return showFeedback({
      type: 'loading',
      message,
      dismissible: false,
      autoHide: false,
      ...options
    });
  }, [showFeedback]);

  const dismissFeedback = useCallback((id: string) => {
    setMessages(prev => prev.filter(msg => msg.id !== id));
  }, []);

  const clearAll = useCallback(() => {
    setMessages([]);
  }, []);

  const updateFeedback = useCallback((id: string, updates: Partial<FeedbackMessage>) => {
    setMessages(prev => prev.map(msg => 
      msg.id === id ? { ...msg, ...updates } : msg
    ));
  }, []);

  return {
    messages,
    showFeedback,
    showSuccess,
    showError,
    showWarning,
    showInfo,
    showLoading,
    dismissFeedback,
    clearAll,
    updateFeedback
  };
}

// Utility functions for common feedback patterns
export const createRetryAction = (onRetry: () => void, loading = false): FeedbackAction => ({
  label: 'Retry',
  onClick: onRetry,
  variant: 'default',
  loading
});

export const createRefreshAction = (onRefresh: () => void): FeedbackAction => ({
  label: 'Refresh Page',
  onClick: onRefresh,
  variant: 'outline'
});

export const createContactSupportAction = (): FeedbackAction => ({
  label: 'Contact Support',
  onClick: () => {
    // You can customize this to open a support modal, redirect to support page, etc.
    window.open('mailto:<EMAIL>?subject=Application Error', '_blank');
  },
  variant: 'ghost'
});

// Helper function to extract error details from various error types
export function extractErrorDetails(error: any): { message: string; details?: string[] } {
  if (typeof error === 'string') {
    return { message: error };
  }

  if (error?.response?.data) {
    const data = error.response.data;
    return {
      message: data.error || data.message || 'An error occurred',
      details: data.details || (data.errors ? Object.values(data.errors).flat() : undefined)
    };
  }

  if (error?.message) {
    return {
      message: error instanceof Error ? error instanceof Error ? error.message : String(error) : String(error),
      details: error.details || (error instanceof Error ? error.stack : String(error) ? [error instanceof Error ? error.stack : String(error)] : undefined)
    };
  }

  return {
    message: 'An unexpected error occurred',
    details: [JSON.stringify(error)]
  };
}

// Hook for handling async operations with feedback
export function useAsyncOperation() {
  const feedback = useFeedback();
  const [isLoading, setIsLoading] = useState(false);

  const execute = useCallback(async <T>(
    operation: () => Promise<T>,
    options: {
      loadingMessage?: string;
      successMessage?: string;
      errorMessage?: string;
      onSuccess?: (result: T) => void;
      onError?: (error: any) => void;
    } = {}
  ): Promise<T | null> => {
    setIsLoading(true);
    
    let loadingId: string | undefined;
    if (options.loadingMessage) {
      loadingId = feedback.showLoading(options.loadingMessage);
    }

    try {
      const result = await operation();
      
      if (loadingId) {
        feedback.dismissFeedback(loadingId);
      }
      
      if (options.successMessage) {
        feedback.showSuccess(options.successMessage);
      }
      
      options.onSuccess?.(result);
      return result;
    } catch (error) {
      if (loadingId) {
        feedback.dismissFeedback(loadingId);
      }
      
      const errorDetails = extractErrorDetails(error);
      feedback.showError(
        options.errorMessage || errorDetails.message,
        { details: errorDetails.details }
      );
      
      options.onError?.(error);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [feedback]);

  return {
    execute,
    isLoading,
    ...feedback
  };
}
