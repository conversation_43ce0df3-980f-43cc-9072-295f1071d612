"use client";

import React, { useState, useCallback } from 'react';
import { signIn, getSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { useCSRF } from '@/hooks/useCSRF';
import { useValidatedForm } from '@/hooks/useFormValidation';
import { FormValidationRules } from '@/lib/client-validation';
import { useFeedback, createRetryAction } from '@/hooks/useFeedback';
import { FeedbackManager, FormErrorDisplay } from '@/components/ui/error-feedback';

const LoginForm = () => {
  const [error, setError] = useState('');
  const [resendMessage, setResendMessage] = useState('');
  const [isResending, setIsResending] = useState(false);
  const [showResendButton, setShowResendButton] = useState(false);
  const router = useRouter();
  const { getHeaders, isLoading: csrfLoading } = useCSRF();
  const feedback = useFeedback();

  // Use the new validation system
  const {
    data,
    updateField,
    handleSubmit: handleValidatedSubmit,
    isSubmitting,
    validation,
    validationActions
  } = useValidatedForm(
    { email: '', password: '' },
    FormValidationRules.login,
    async (formData, sanitizedData) => {
      await performLogin(sanitizedData.email, sanitizedData.password);
    }
  );

  const performLogin = useCallback(async (email: string, password: string) => {
    setError('');
    setResendMessage('');
    setShowResendButton(false);

    try {
      const result = await signIn('credentials', {
        email,
        password,
        redirect: false,
      });

      if (result?.error) {
        setError(result.error);
        // Check if this is an email verification error
        if (result.error.toLowerCase().includes('verify your email')) {
          setShowResendButton(true);
          feedback.showError(result.error, {
            title: 'Email Verification Required',
            actions: [createRetryAction(() => handleResendVerification(), isResending)]
          });
        } else {
          feedback.showError(result.error, {
            title: 'Login Failed'
          });
        }
        return; // Don't throw error, just return to show the error state
      } else if (result?.ok) {
        // Get the updated session to ensure user is logged in
        const session = await getSession();
        if (session) {
          feedback.showSuccess('Login successful! Redirecting...', { autoHide: true, duration: 2000 });
          // Check for callback URL from middleware redirect or URL params
          const urlParams = new URLSearchParams(window.location.search);
          const callbackUrl = urlParams.get('callbackUrl') || urlParams.get('redirect') || '/dashboard';
          router.push(callbackUrl);
          router.refresh();
        }
      }
    } catch (err) {
      setError('An unexpected error occurred. Please try again.');
      feedback.showError('An unexpected error occurred. Please try again.', {
        title: 'Login Error',
        details: [err instanceof Error ? err instanceof Error ? err instanceof Error ? err.message : String(err) : String(err) : 'Unknown error']
      });
      console.error('Login error:', err);
      throw err; // Re-throw to let the form handler know it failed
    }
  }, [router, feedback, createRetryAction, isResending]);

  const handleResendVerification = async () => {
    if (!data.email) return;

    setIsResending(true);
    setResendMessage('');

    try {
      const response = await fetch('/api/auth/resend-verification', {
        method: 'POST',
        headers: getHeaders(),
        body: JSON.stringify({ email: data.email }),
      });

      const responseData = await response.json();

      if (response.ok) {
        setResendMessage('Verification email sent! Please check your inbox.');
        setShowResendButton(false);
        feedback.showSuccess('Verification email sent! Please check your inbox.');
      } else {
        setResendMessage(responseData.error || 'Failed to send verification email.');
        feedback.showError(responseData.error || 'Failed to send verification email.');
      }
    } catch (error) {
      console.error('Resend verification error:', error);
      setResendMessage('An unexpected error occurred.');
      feedback.showError('An unexpected error occurred while sending verification email.');
    } finally {
      setIsResending(false);
    }
  };

  return (
    <div className="flex items-center justify-center min-h-[calc(100vh-80px)] bg-gray-50 dark:bg-gray-950">
      <Card className="w-full max-w-sm">
        <CardHeader className="space-y-1">
          <h1 className="text-2xl font-bold">Sign In</h1>
          <CardDescription>
            Enter your email and password to access your account
          </CardDescription>
        </CardHeader>
        <form onSubmit={handleValidatedSubmit}>
          <CardContent className="grid gap-4">
            {/* Feedback Messages */}
            <FeedbackManager
              messages={feedback.messages}
              onDismiss={feedback.dismissFeedback}
            />

            {/* Form Validation Errors */}
            <FormErrorDisplay errors={validation.errors} />

            {/* Direct Error Display for Tests */}
            {error && (
              <div role="alert" className="text-red-600 text-sm bg-red-50 p-3 rounded-md border border-red-200">
                {error}
              </div>
            )}

            <div className="grid gap-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                value={data.email}
                onChange={(e) => updateField('email', e.target.value)}
                required
                disabled={isSubmitting}
                autoComplete="email"
                name="email"
                className={validation.errors.email ? 'border-red-500' : ''}
              />
              {validation.errors.email && (
                <div className="text-red-500 text-sm">{validation.errors.email}</div>
              )}
            </div>
            <div className="grid gap-2">
              <div className="flex items-center">
                <Label htmlFor="password">Password</Label>
                <Link
                  href="/auth/forgot-password"
                  className="ml-auto inline-block text-sm underline"
                >
                  Forgot your password?
                </Link>
              </div>
              <Input
                id="password"
                type="password"
                value={data.password}
                onChange={(e) => updateField('password', e.target.value)}
                required
                disabled={isSubmitting}
                autoComplete="current-password"
                name="password"
                className={validation.errors.password ? 'border-red-500' : ''}
              />
              {validation.errors.password && (
                <div className="text-red-500 text-sm">{validation.errors.password}</div>
              )}
            </div>
            {error && (
              <div className="text-red-500 text-sm bg-red-50 dark:bg-red-900/20 p-2 rounded-md">
                {error}
              </div>
            )}
            {resendMessage && (
              <div className={`text-sm p-2 rounded-md ${
                resendMessage.includes('sent') || resendMessage.includes('successfully')
                  ? 'text-green-700 bg-green-50 dark:bg-green-900/20'
                  : 'text-red-500 bg-red-50 dark:bg-red-900/20'
              }`}>
                {resendMessage}
              </div>
            )}
          </CardContent>
          <CardFooter className="flex flex-col space-y-4">
            <Button
              className="w-full"
              type="submit"
              disabled={isSubmitting || csrfLoading || !validation.isValid}
            >
              {isSubmitting ? 'Signing in...' : csrfLoading ? 'Loading...' : 'Sign in'}
            </Button>
            {showResendButton && (
              <Button
                type="button"
                variant="outline"
                className="w-full"
                onClick={handleResendVerification}
                disabled={isResending}
              >
                {isResending ? 'Sending...' : 'Resend verification email'}
              </Button>
            )}
            <div className="text-center text-sm text-gray-500">
              Don't have an account?{" "}
              <Link href="/signup" className="underline">
                Sign up
              </Link>
            </div>
          </CardFooter>
        </form>
      </Card>
    </div>
  );
};

export default LoginForm;
