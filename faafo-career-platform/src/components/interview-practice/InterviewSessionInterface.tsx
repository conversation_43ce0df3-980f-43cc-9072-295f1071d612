'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Skeleton } from '@/components/ui/skeleton';
import { useCSRFToken } from '@/hooks/useCSRFToken';
import { useMemoryManagement } from '@/hooks/useMemoryManagement';
import InterviewPracticeErrorBoundary from '@/components/error-boundary/InterviewPracticeErrorBoundary';
import {
  Play,
  Pause,
  Square,
  SkipForward,
  ChevronLeft,
  ChevronRight,
  Clock,
  Mic,
  <PERSON><PERSON><PERSON><PERSON>,
  Lightbulb,
  Target,
  CheckCircle,
  AlertCircle,
  Home,
  RotateCcw
} from 'lucide-react';
import { getUserFriendlyError } from '@/lib/user-friendly-errors';

interface InterviewQuestion {
  id: string;
  questionText: string;
  questionType: string;
  category: string;
  difficulty: string;
  expectedDuration: number;
  context?: string;
  hints?: any;
  followUpQuestions?: string[];
  questionOrder: number;
  responses?: InterviewResponse[];
}

interface InterviewResponse {
  id: string;
  responseText?: string;
  audioUrl?: string;
  responseTime: number;
  preparationTime: number;
  aiScore?: number;
  aiAnalysis?: any;
  feedback?: any;
  isCompleted: boolean;
  userNotes?: string;
}

interface InterviewSession {
  id: string;
  sessionType: string;
  careerPath?: string;
  experienceLevel?: string;
  status: string;
  totalQuestions: number;
  completedQuestions: number;
  overallScore?: number;
  timeSpent: number;
  startedAt?: string;
  questions?: InterviewQuestion[];
  progress?: {
    completed: number;
    total: number;
    percentage: number;
  };
}

interface InterviewSessionInterfaceProps {
  session: InterviewSession;
  onSessionComplete: () => void;
  onBackToDashboard: () => void;
}

export default function InterviewSessionInterface({
  session,
  onSessionComplete,
  onBackToDashboard,
}: InterviewSessionInterfaceProps) {
  const { csrfFetch, isLoading: csrfLoading } = useCSRFToken();
  const memoryManager = useMemoryManagement();
  const [questions, setQuestions] = useState<InterviewQuestion[]>([]);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [response, setResponse] = useState('');
  const [userNotes, setUserNotes] = useState('');
  const [isRecording, setIsRecording] = useState(false);
  const [isPreparing, setIsPreparing] = useState(true);
  const [preparationTime, setPreparationTime] = useState(0);
  const [responseTime, setResponseTime] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showHints, setShowHints] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [sessionStatus, setSessionStatus] = useState(session.status);

  // Timer IDs for memory management
  const preparationTimerIdRef = useRef<string | null>(null);
  const responseTimerIdRef = useRef<string | null>(null);

  useEffect(() => {
    // Only load questions when CSRF token is available
    if (!csrfLoading) {
      loadSessionQuestions();
    }
    // Memory management handles cleanup automatically
  }, [session.id, csrfLoading]);

  useEffect(() => {
    // Start preparation timer when component mounts or question changes
    if (isPreparing) {
      startPreparationTimer();
    }
    // Memory management handles cleanup automatically
  }, [currentQuestionIndex, isPreparing]);

  // Load existing response when question changes
  useEffect(() => {
    if (questions.length > 0 && currentQuestionIndex < questions.length) {
      const currentQuestion = questions[currentQuestionIndex];
      const existingResponse = currentQuestion?.responses?.[0];

      if (existingResponse && existingResponse.isCompleted) {
        // Show completed response
        setResponse(existingResponse.responseText || '');
        setUserNotes(existingResponse.userNotes || '');
        setIsPreparing(false);
      }
      // Note: We don't reset to fresh state here to avoid conflicts with navigation functions
    }
  }, [currentQuestionIndex, questions]);

  const loadSessionQuestions = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      // First check if questions exist
      let response = await fetch(`/api/interview-practice/${session.id}/questions`, {
        credentials: 'same-origin',
        signal: AbortSignal.timeout(10000) // 10 second timeout
      });

      if (!response.ok) {
        throw new Error('Failed to load questions');
      }

      let data = await response.json();

      if (data.success && data.data.length > 0) {
        setQuestions(data.data);
        // Find the first incomplete question
        const incompleteIndex = data.data.findIndex((q: InterviewQuestion) =>
          !q.responses || q.responses.length === 0 || !q.responses[0]?.isCompleted
        );
        setCurrentQuestionIndex(incompleteIndex >= 0 ? incompleteIndex : 0);
      } else {
        // Generate questions if they don't exist - check if CSRF token is available
        if (csrfLoading) {
          setError('Loading authentication token...');
          return;
        }

        // Add timeout to prevent hanging - increased to 30 seconds for AI generation
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

        const generateResponse = await csrfFetch(`/api/interview-practice/${session.id}/questions`, {
          method: 'POST',
          body: JSON.stringify({
            count: session.totalQuestions,
          }),
          signal: controller.signal,
        });

        clearTimeout(timeoutId);

        if (!generateResponse.ok) {
          throw new Error(`HTTP ${generateResponse.status}`);
        }

        const generateData = await generateResponse.json();

        if (generateData.success) {
          setQuestions(generateData.data.questions);
        } else {
          throw new Error(generateData.error || 'Failed to generate questions');
        }
      }
    } catch (error) {
      console.error('Error loading questions:', error);

      // Handle specific error types
      if (error instanceof Error && error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.name : String(error) : String(error) : String(error) : String(error) === 'AbortError') {
        const friendlyError = getUserFriendlyError('TIMEOUT', 'interview');
        setError(friendlyError.message);
      } else {
        const friendlyError = getUserFriendlyError(error, 'interview');
        setError(friendlyError.message);
      }
    } finally {
      setIsLoading(false);
    }
  }, [session.id, session.totalQuestions, csrfLoading, csrfFetch]);

  const startPreparationTimer = useCallback(() => {
    setPreparationTime(0);
    // Clear existing timer if any
    if (preparationTimerIdRef.current) {
      memoryManager.clearTimer(preparationTimerIdRef.current);
    }
    // Create new managed timer
    preparationTimerIdRef.current = memoryManager.createInterval(() => {
      setPreparationTime(prev => prev + 1);
    }, 1000, 'preparation-timer');
  }, [memoryManager]);

  const startResponseTimer = useCallback(() => {
    setResponseTime(0);
    // Clear existing timer if any
    if (responseTimerIdRef.current) {
      memoryManager.clearTimer(responseTimerIdRef.current);
    }
    // Create new managed timer
    responseTimerIdRef.current = memoryManager.createInterval(() => {
      setResponseTime(prev => prev + 1);
    }, 1000, 'response-timer');
  }, [memoryManager]);

  const stopAllTimers = useCallback(() => {
    if (preparationTimerIdRef.current) {
      memoryManager.clearTimer(preparationTimerIdRef.current);
      preparationTimerIdRef.current = null;
    }
    if (responseTimerIdRef.current) {
      memoryManager.clearTimer(responseTimerIdRef.current);
      responseTimerIdRef.current = null;
    }
  }, [memoryManager]);

  const handleStartResponse = useCallback(() => {
    setIsPreparing(false);
    stopAllTimers();
    startResponseTimer();
  }, [stopAllTimers, startResponseTimer]);

  const handleSubmitResponse = useCallback(async () => {
    try {
      setIsSubmitting(true);
      setError(null);
      stopAllTimers();

      const currentQuestion = questions[currentQuestionIndex];

      // Check if this is the last question and we're trying to complete the session
      const isLastQuestion = currentQuestionIndex === session.totalQuestions - 1;

      // Only submit response if there's text and no existing response
      const hasExistingResponse = currentQuestion.responses && currentQuestion.responses.length > 0;
      const shouldSubmitResponse = response.trim() && !hasExistingResponse;

      if (shouldSubmitResponse) {
        const submitResponse = await csrfFetch(`/api/interview-practice/${session.id}/responses`, {
          method: 'POST',
          body: JSON.stringify({
            questionId: currentQuestion.id,
            responseText: response,
            responseTime,
            preparationTime,
            userNotes: userNotes.trim() || undefined,
            requestFeedback: true,
          }),
        });

        if (!submitResponse.ok) {
          const errorData = await submitResponse.json();
          // If response already exists, that's okay for completion
          if (submitResponse.status === 400 && errorData.error?.includes('already exists')) {
            console.log('Response already exists, proceeding with session completion');
          } else {
            throw new Error(errorData.error || 'Failed to submit response');
          }
        } else {
          const data = await submitResponse.json();

          if (data.success) {
            // Update the question with the response
            const updatedQuestions = [...questions];
            updatedQuestions[currentQuestionIndex] = {
              ...updatedQuestions[currentQuestionIndex],
              responses: [data.data],
            };
            setQuestions(updatedQuestions);
          }
        }
      }

      // Handle navigation or completion
      if (isLastQuestion) {
        // Session complete - inline to avoid circular dependency
        try {
          // Calculate completed questions count more accurately
          const completedCount = questions.filter(q =>
            q.responses && q.responses.length > 0 && q.responses[0].isCompleted
          ).length;

          // Ensure we have a minimum completion threshold (at least 50% of questions or all questions)
          const minimumRequired = Math.ceil(session.totalQuestions * 0.5);
          const canComplete = completedCount >= minimumRequired || completedCount === session.totalQuestions;

          if (!canComplete) {
            setError(`Please complete at least ${minimumRequired} questions before finishing the session.`);
            return;
          }

          // Calculate time spent more accurately
          const startTime = session.startedAt ? new Date(session.startedAt).getTime() : Date.now();
          const timeSpentMinutes = Math.floor((Date.now() - startTime) / 60000);

          const completeResponse = await csrfFetch(`/api/interview-practice/${session.id}`, {
            method: 'PATCH',
            body: JSON.stringify({
              status: 'COMPLETED',
              completedQuestions: completedCount,
              timeSpent: Math.max(1, timeSpentMinutes), // Ensure at least 1 minute
            }),
          });

          if (completeResponse.ok) {
            const responseData = await completeResponse.json();
            setSessionStatus('COMPLETED');
            onSessionComplete();
          } else {
            const errorData = await completeResponse.json().catch(() => ({}));
            setError(errorData.error || `Failed to complete session (${completeResponse.status}). Please try again.`);
          }
        } catch (error) {
          console.error('Error completing session:', error);
          const errorMessage = error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : String(error) : String(error) : String(error) : String(error) : String(error) : 'Failed to complete session. Please try again.';
          setError(errorMessage);
        }
      } else {
        // Move to next question
        const newIndex = currentQuestionIndex + 1;
        setCurrentQuestionIndex(newIndex);

        // Load existing response if available
        const nextQuestion = questions[newIndex];
        const existingResponse = nextQuestion?.responses?.[0];

        if (existingResponse && existingResponse.isCompleted) {
          // Show completed response
          setResponse(existingResponse.responseText || '');
          setUserNotes(existingResponse.userNotes || '');
          setIsPreparing(false);
        } else {
          // No response yet, start fresh
          setResponse('');
          setUserNotes('');
          setIsPreparing(true);
        }

        setShowHints(false);
      }
    } catch (error) {
      console.error('Error submitting response:', error);
      setError('Failed to submit response. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  }, [questions, currentQuestionIndex, session.id, session.totalQuestions, session.startedAt, response, responseTime, preparationTime, userNotes, stopAllTimers, csrfFetch, onSessionComplete]);

  const completeSession = useCallback(async () => {
    try {
      setIsSubmitting(true);
      setError('');

      // Calculate completed questions count more accurately
      const completedCount = questions.filter(q =>
        q.responses && q.responses.length > 0 && q.responses[0].isCompleted
      ).length;

      // Ensure we have a minimum completion threshold (at least 50% of questions or all questions)
      const minimumRequired = Math.ceil(session.totalQuestions * 0.5);
      const canComplete = completedCount >= minimumRequired || completedCount === session.totalQuestions;

      if (!canComplete) {
        setError(`Please complete at least ${minimumRequired} questions before finishing the session.`);
        return;
      }

      // Calculate time spent more accurately
      const startTime = session.startedAt ? new Date(session.startedAt).getTime() : Date.now();
      const timeSpentMinutes = Math.floor((Date.now() - startTime) / 60000);

      console.log('Completing session with data:', {
        sessionId: session.id,
        status: 'COMPLETED',
        completedQuestions: completedCount,
        timeSpent: Math.max(1, timeSpentMinutes)
      });

      const response = await csrfFetch(`/api/interview-practice/${session.id}`, {
        method: 'PATCH',
        body: JSON.stringify({
          status: 'COMPLETED',
          completedQuestions: completedCount,
          timeSpent: Math.max(1, timeSpentMinutes), // Ensure at least 1 minute
        }),
      });

      console.log('Session completion response:', response.status, response.statusText);

      if (response.ok) {
        const responseData = await response.json();
        console.log('Session completion successful:', responseData);
        setSessionStatus('COMPLETED');
        onSessionComplete();
      } else {
        const errorData = await response.json().catch(() => ({}));
        console.error('Failed to complete session:', response.status, response.statusText, errorData);
        setError(errorData.error || `Failed to complete session (${response.status}). Please try again.`);
      }
    } catch (error) {
      console.error('Error completing session:', error);
      const errorMessage = error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : String(error) : String(error) : String(error) : String(error) : String(error) : 'Failed to complete session. Please try again.';
      setError(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  }, [questions, session.id, session.totalQuestions, session.startedAt, csrfFetch, onSessionComplete]);

  const handleSkipQuestion = useCallback(() => {
    if (currentQuestionIndex < session.totalQuestions - 1) {
      const newIndex = currentQuestionIndex + 1;
      setCurrentQuestionIndex(newIndex);

      // Load existing response if available
      const nextQuestion = questions[newIndex];
      const existingResponse = nextQuestion?.responses?.[0];

      if (existingResponse && existingResponse.isCompleted) {
        // Show completed response
        setResponse(existingResponse.responseText || '');
        setUserNotes(existingResponse.userNotes || '');
        setIsPreparing(false);
      } else {
        // No response yet, start fresh
        setResponse('');
        setUserNotes('');
        setIsPreparing(true);
      }

      setShowHints(false);
      stopAllTimers();
    }
  }, [currentQuestionIndex, session.totalQuestions, questions, stopAllTimers]);

  const handlePreviousQuestion = useCallback(() => {
    if (currentQuestionIndex > 0) {
      const newIndex = currentQuestionIndex - 1;
      setCurrentQuestionIndex(newIndex);

      // Load existing response if available
      const previousQuestion = questions[newIndex];
      const existingResponse = previousQuestion?.responses?.[0];

      if (existingResponse && existingResponse.isCompleted) {
        // Show completed response
        setResponse(existingResponse.responseText || '');
        setUserNotes(existingResponse.userNotes || '');
        setIsPreparing(false);
      } else {
        // No response yet, start fresh
        setResponse('');
        setUserNotes('');
        setIsPreparing(true);
      }

      setShowHints(false);
      stopAllTimers();
    }
  }, [currentQuestionIndex, questions, stopAllTimers]);

  const formatTime = useCallback((seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  }, []);

  const getCurrentQuestion = () => questions[currentQuestionIndex];
  const currentQuestion = getCurrentQuestion();
  const progress = session.totalQuestions > 0 ? ((currentQuestionIndex + 1) / session.totalQuestions) * 100 : 0;
  const completedQuestions = questions.filter(q => q.responses && q.responses.length > 0 && q.responses[0].isCompleted).length;

  if (isLoading || csrfLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card className="w-full max-w-4xl mx-auto">
          <CardHeader>
            <Skeleton className="h-8 w-64" />
            <Skeleton className="h-4 w-96" />
            <Skeleton className="h-2 w-full mt-4" />
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 dark:border-gray-100 mx-auto mb-4"></div>
              <p className="text-gray-600 dark:text-gray-400">Loading interview questions...</p>
              <p className="text-sm text-gray-500 dark:text-gray-500 mt-2">
                {questions.length === 0 ? 'Generating personalized questions' : 'Preparing your session'}
              </p>
            </div>
            <Skeleton className="h-32 w-full" />
            <Skeleton className="h-24 w-full" />
            <div className="flex justify-between">
              <Skeleton className="h-10 w-24" />
              <Skeleton className="h-10 w-32" />
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card className="w-full max-w-4xl mx-auto">
          <CardContent className="p-8 text-center">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">Error Loading Session</h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4">{error}</p>
            <div className="flex justify-center space-x-4">
              <Button variant="outline" onClick={onBackToDashboard}>
                <Home className="h-4 w-4 mr-2" />
                Back to Dashboard
              </Button>
              <Button onClick={() => window.location.reload()}>
                <RotateCcw className="h-4 w-4 mr-2" />
                Retry
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!currentQuestion) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card className="w-full max-w-4xl mx-auto">
          <CardContent className="p-8 text-center">
            <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">Session Complete!</h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              You've completed all questions in this practice session.
            </p>
            <Button onClick={onSessionComplete}>
              View Results
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <InterviewPracticeErrorBoundary>
      <div className="container mx-auto px-4 py-8">
        <Card className="w-full max-w-4xl mx-auto">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center space-x-2">
                  <Target className="h-5 w-5" />
                  <span>Interview Practice Session</span>
                </CardTitle>
                <CardDescription>
                  {session.sessionType.replace('_', ' ')} • Question {currentQuestionIndex + 1} of {session.totalQuestions}
                </CardDescription>
              </div>
              <div className="flex items-center space-x-4">
                <Badge variant="outline">
                  {completedQuestions}/{session.totalQuestions} completed
                </Badge>
                <Button variant="ghost" size="sm" onClick={onBackToDashboard}>
                  <Home className="h-4 w-4 mr-2" />
                  Dashboard
                </Button>
              </div>
            </div>
            <Progress value={progress} className="mt-4" />
          </CardHeader>

        <CardContent className="space-y-6">
          {/* Question Display */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Badge variant="secondary">{currentQuestion.questionType.replace('_', ' ')}</Badge>
                  <Badge variant="outline">{currentQuestion.category.replace('_', ' ')}</Badge>
                  <Badge variant="outline">{currentQuestion.difficulty}</Badge>
                </div>
                <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
                  <Clock className="h-4 w-4" />
                  <span>Expected: {Math.floor(currentQuestion.expectedDuration / 60)}m</span>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <h3 className="text-lg font-semibold mb-4">{currentQuestion.questionText}</h3>
              
              {currentQuestion.context && (
                <div className="bg-blue-50 dark:bg-blue-950 p-4 rounded-lg mb-4">
                  <p className="text-sm text-blue-800 dark:text-blue-200">
                    <strong>Context:</strong> {currentQuestion.context}
                  </p>
                </div>
              )}

              {showHints && currentQuestion.hints && (
                <div className="bg-yellow-50 dark:bg-yellow-950 p-4 rounded-lg mb-4">
                  <div className="flex items-start space-x-2">
                    <Lightbulb className="h-5 w-5 text-yellow-600 dark:text-yellow-400 mt-0.5" />
                    <div>
                      <h4 className="font-medium text-yellow-800 dark:text-yellow-200 mb-2">Hints:</h4>
                      {currentQuestion.hints.structure && (
                        <p className="text-sm text-yellow-700 dark:text-yellow-300 mb-2">
                          <strong>Structure:</strong> {currentQuestion.hints.structure}
                        </p>
                      )}
                      {currentQuestion.hints.keyPoints && (
                        <div className="text-sm text-yellow-700 dark:text-yellow-300">
                          <strong>Key Points:</strong>
                          <ul className="list-disc list-inside mt-1">
                            {currentQuestion.hints.keyPoints.map((point: string, index: number) => (
                              <li key={index}>{point}</li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )}

              <div className="flex items-center justify-between">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowHints(!showHints)}
                >
                  <Lightbulb className="h-4 w-4 mr-2" />
                  {showHints ? 'Hide Hints' : 'Show Hints'}
                </Button>
                
                <div className="flex items-center space-x-4 text-sm">
                  {isPreparing ? (
                    <div className="flex items-center space-x-2 text-blue-600 dark:text-blue-400">
                      <Clock className="h-4 w-4" />
                      <span>Prep: {formatTime(preparationTime)}</span>
                    </div>
                  ) : (
                    <div className="flex items-center space-x-2 text-green-600 dark:text-green-400">
                      <Clock className="h-4 w-4" />
                      <span>Response: {formatTime(responseTime)}</span>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Response Area */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Your Response</CardTitle>
              <CardDescription>
                {isPreparing 
                  ? "Take your time to think about your answer. Click 'Start Response' when ready."
                  : "Record your response to the interview question."
                }
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {isPreparing ? (
                <div className="text-center py-8">
                  <Clock className="h-12 w-12 text-blue-500 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">Preparation Time</h3>
                  <p className="text-gray-600 dark:text-gray-400 mb-4">
                    Use this time to think about your answer and review any hints.
                  </p>
                  <div className="text-2xl font-bold text-blue-600 dark:text-blue-400 mb-4">
                    {formatTime(preparationTime)}
                  </div>
                  <Button onClick={handleStartResponse} size="lg">
                    <Play className="h-5 w-5 mr-2" />
                    Start Response
                  </Button>
                </div>
              ) : (
                <>
                  <div className="space-y-2">
                    <Textarea
                      value={response}
                      onChange={(e) => {
                        const value = e.target.value;
                        if (value.length <= 5000) { // 5000 character limit
                          setResponse(value);
                          if (error && error.includes('response')) {
                            setError('');
                          }
                        }
                      }}
                      placeholder="Type your response here..."
                      className="min-h-32"
                      disabled={isSubmitting}
                      maxLength={5000}
                    />
                    <div className="flex justify-between text-xs text-gray-500">
                      <span>Response length: {response.length}/5000 characters</span>
                      {response.length > 4500 && (
                        <span className="text-orange-600">Approaching character limit</span>
                      )}
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Textarea
                      value={userNotes}
                      onChange={(e) => {
                        const value = e.target.value;
                        if (value.length <= 1000) { // 1000 character limit for notes
                          setUserNotes(value);
                        }
                      }}
                      placeholder="Optional: Add notes about your thought process..."
                      className="min-h-20"
                      disabled={isSubmitting}
                      maxLength={1000}
                    />
                    <div className="flex justify-between text-xs text-gray-500">
                      <span>Notes length: {userNotes.length}/1000 characters</span>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Button variant="outline" size="sm" disabled>
                        <Mic className="h-4 w-4 mr-2" />
                        Audio (Coming Soon)
                      </Button>
                    </div>
                    
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      Response time: {formatTime(responseTime)}
                    </div>
                  </div>
                </>
              )}
            </CardContent>
          </Card>

          {error && (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Navigation */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                onClick={handlePreviousQuestion}
                disabled={currentQuestionIndex === 0 || isSubmitting}
              >
                <ChevronLeft className="h-4 w-4 mr-2" />
                Previous
              </Button>
              
              <Button
                variant="ghost"
                onClick={handleSkipQuestion}
                disabled={currentQuestionIndex === session.totalQuestions - 1 || isSubmitting}
              >
                <SkipForward className="h-4 w-4 mr-2" />
                Skip
              </Button>
            </div>

            <div className="flex items-center space-x-2">
              {!isPreparing && (
                <Button
                  onClick={handleSubmitResponse}
                  disabled={isSubmitting || (!response.trim() && currentQuestionIndex < session.totalQuestions - 1)}
                  size="lg"
                >
                  {isSubmitting ? (
                    'Submitting...'
                  ) : currentQuestionIndex === session.totalQuestions - 1 ? (
                    'Complete Session'
                  ) : (
                    <>
                      Submit & Next
                      <ChevronRight className="h-4 w-4 ml-2" />
                    </>
                  )}
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
    </InterviewPracticeErrorBoundary>
  );
}
