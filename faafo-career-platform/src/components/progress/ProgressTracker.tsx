'use client';

import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { CheckCircle, Clock, BookOpen, Star, TrendingUp, Target, Award, Calendar } from 'lucide-react';
import { Button } from '@/components/ui/button';
import Link from 'next/link';

interface ProgressData {
  totalResources: number;
  completedResources: number;
  inProgressResources: number;
  bookmarkedResources: number;
  averageRating: number;
  totalRatings: number;
  streakDays: number;
  weeklyGoal: number;
  weeklyProgress: number;
  recentActivity: {
    id: string;
    resourceTitle: string;
    status: string;
    date: string;
    rating?: number;
  }[];
  achievements: {
    id: string;
    title: string;
    description: string;
    icon: string;
    unlockedAt?: string;
  }[];
}

interface ProgressTrackerProps {
  userId?: string;
  compact?: boolean;
}

export default function ProgressTracker({ userId, compact = false }: ProgressTrackerProps) {
  const { data: session } = useSession();
  const [progressData, setProgressData] = useState<ProgressData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const effectiveUserId = userId || session?.user?.id;

  useEffect(() => {
    if (effectiveUserId) {
      fetchProgressData();
    }
  }, [effectiveUserId]);

  const fetchProgressData = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/progress-tracker?userId=${effectiveUserId}`);
      
      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setProgressData(result.data);
        } else {
          throw new Error(result.error || 'Failed to fetch progress data');
        }
      } else {
        throw new Error('Failed to fetch progress data');
      }
    } catch (error) {
      console.error('Error fetching progress data:', error);
      setError(error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : String(error) : String(error) : 'Failed to load progress data');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-md">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded mb-4"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !progressData) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-md">
        <div className="text-center">
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            {error || 'Unable to load progress data'}
          </p>
          <Button onClick={fetchProgressData} variant="outline" size="sm">
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  const completionRate = progressData.totalResources > 0 
    ? (progressData.completedResources / progressData.totalResources) * 100 
    : 0;

  const weeklyCompletionRate = progressData.weeklyGoal > 0 
    ? (progressData.weeklyProgress / progressData.weeklyGoal) * 100 
    : 0;

  if (compact) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-md">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
          Learning Progress
        </h3>
        
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
              {progressData.completedResources}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">Completed</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-orange-600 dark:text-orange-400">
              {progressData.inProgressResources}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">In Progress</div>
          </div>
        </div>

        <div className="mb-4">
          <div className="flex justify-between text-sm mb-1">
            <span className="text-gray-600 dark:text-gray-400">Overall Progress</span>
            <span className="text-gray-900 dark:text-gray-100">{completionRate.toFixed(1)}%</span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div 
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${Math.min(completionRate, 100)}%` }}
            ></div>
          </div>
        </div>

        <Button asChild variant="outline" size="sm" className="w-full">
          <Link href="/dashboard?tab=progress">View Full Progress</Link>
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-md">
          <div className="flex items-center gap-3">
            <CheckCircle className="h-8 w-8 text-green-500" />
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Completed</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {progressData.completedResources}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-md">
          <div className="flex items-center gap-3">
            <Clock className="h-8 w-8 text-orange-500" />
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">In Progress</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {progressData.inProgressResources}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-md">
          <div className="flex items-center gap-3">
            <BookOpen className="h-8 w-8 text-blue-500" />
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Bookmarked</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {progressData.bookmarkedResources}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-md">
          <div className="flex items-center gap-3">
            <TrendingUp className="h-8 w-8 text-purple-500" />
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Streak</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {progressData.streakDays} days
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Progress Bars */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-md">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
            Overall Progress
          </h3>
          <div className="mb-2">
            <div className="flex justify-between text-sm mb-1">
              <span className="text-gray-600 dark:text-gray-400">
                {progressData.completedResources} of {progressData.totalResources} resources
              </span>
              <span className="text-gray-900 dark:text-gray-100">{completionRate.toFixed(1)}%</span>
            </div>
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
              <div 
                className="bg-gray-600 h-3 rounded-full transition-all duration-300"
                style={{ width: `${Math.min(completionRate, 100)}%` }}
              ></div>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-md">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
            Weekly Goal
          </h3>
          <div className="mb-2">
            <div className="flex justify-between text-sm mb-1">
              <span className="text-gray-600 dark:text-gray-400">
                {progressData.weeklyProgress} of {progressData.weeklyGoal} resources
              </span>
              <span className="text-gray-900 dark:text-gray-100">{weeklyCompletionRate.toFixed(1)}%</span>
            </div>
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
              <div 
                className="bg-green-600 h-3 rounded-full transition-all duration-300"
                style={{ width: `${Math.min(weeklyCompletionRate, 100)}%` }}
              ></div>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-md">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
          Recent Activity
        </h3>
        {progressData.recentActivity.length > 0 ? (
          <div className="space-y-3">
            {progressData.recentActivity.slice(0, 5).map((activity) => (
              <div key={activity.id} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div className="flex items-center gap-3">
                  <div className={`w-2 h-2 rounded-full ${
                    activity.status === 'COMPLETED' ? 'bg-green-500' :
                    activity.status === 'IN_PROGRESS' ? 'bg-orange-500' :
                    'bg-blue-500'
                  }`}></div>
                  <div>
                    <p className="font-medium text-gray-900 dark:text-gray-100">
                      {activity.resourceTitle}
                    </p>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {activity.status.replace('_', ' ').toLowerCase()}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {new Date(activity.date).toLocaleDateString()}
                  </p>
                  {activity.rating && (
                    <div className="flex items-center gap-1">
                      <Star className="h-3 w-3 text-yellow-500 fill-current" />
                      <span className="text-sm text-gray-600 dark:text-gray-400">
                        {activity.rating}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <p className="text-gray-600 dark:text-gray-400 text-center py-4">
            No recent activity. Start learning to see your progress here!
          </p>
        )}
      </div>

      {/* Achievements */}
      {progressData.achievements.length > 0 && (
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-md">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
            Achievements
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {progressData.achievements.map((achievement) => (
              <div key={achievement.id} className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <Award className="h-6 w-6 text-yellow-500" />
                <div>
                  <p className="font-medium text-gray-900 dark:text-gray-100">
                    {achievement.title}
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {achievement.description}
                  </p>
                  {achievement.unlockedAt && (
                    <p className="text-xs text-gray-500 dark:text-gray-500">
                      Unlocked {new Date(achievement.unlockedAt).toLocaleDateString()}
                    </p>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
