/**
 * Enhanced Error Boundary with Recovery Mechanisms
 * Provides automatic error recovery, retry logic, and graceful degradation
 */

'use client';

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { AlertTriangle, RefreshCw, Home, Shield, Clock } from 'lucide-react';
import Link from 'next/link';
import { ErrorRecoveryManager } from '@/lib/error-recovery';
import { errorTracker } from '@/lib/errorTracking';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  maxRetries?: number;
  retryDelay?: number;
  autoRetry?: boolean;
  context?: string;
  gracefulDegradation?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
  retryCount: number;
  isRetrying: boolean;
  lastRetryTime: number;
  canAutoRetry: boolean;
}

export class EnhancedErrorBoundary extends Component<Props, State> {
  private retryTimer: NodeJS.Timeout | null = null;

  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      retryCount: 0,
      isRetrying: false,
      lastRetryTime: 0,
      canAutoRetry: true
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    const { context = 'unknown', maxRetries = 3, autoRetry = false, retryDelay = 2000 } = this.props;

    console.error('Enhanced Error Boundary caught error:', error, errorInfo);
    
    this.setState({
      error,
      errorInfo,
      lastRetryTime: Date.now()
    });

    // Track error with context
    errorTracker.captureException(error, {
      tags: { 
        context,
        errorBoundary: 'enhanced',
        retryCount: this.state.retryCount.toString()
      },
      extra: {
        componentStack: errorInfo.componentStack,
        retryCount: this.state.retryCount,
        autoRetry
      }
    });

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // Auto-retry logic
    if (autoRetry && this.state.retryCount < maxRetries && this.state.canAutoRetry) {
      this.scheduleRetry(retryDelay);
    }
  }

  componentWillUnmount() {
    if (this.retryTimer) {
      clearTimeout(this.retryTimer);
    }
  }

  scheduleRetry = (delay: number) => {
    this.setState({ isRetrying: true });

    this.retryTimer = setTimeout(() => {
      this.handleRetry(true);
    }, delay);
  };

  handleRetry = (isAutoRetry = false) => {
    const { maxRetries = 3 } = this.props;

    if (this.retryTimer) {
      clearTimeout(this.retryTimer);
      this.retryTimer = null;
    }

    const newRetryCount = this.state.retryCount + 1;

    // Check if we've exceeded max retries
    if (newRetryCount > maxRetries) {
      this.setState({
        isRetrying: false,
        canAutoRetry: false
      });
      
      errorTracker.captureMessage(
        `Max retries exceeded (${maxRetries}) for error boundary`,
        {
          tags: { 
            context: this.props.context || 'unknown',
            errorBoundary: 'enhanced',
            maxRetriesExceeded: 'true'
          }
        }
      );
      return;
    }

    // Log retry attempt
    errorTracker.captureMessage(
      `Error boundary retry attempt ${newRetryCount}/${maxRetries}`,
      {
        tags: { 
          context: this.props.context || 'unknown',
          errorBoundary: 'enhanced',
          retryType: isAutoRetry ? 'auto' : 'manual'
        }
      }
    );

    this.setState({
      hasError: false,
      error: undefined,
      errorInfo: undefined,
      retryCount: newRetryCount,
      isRetrying: false,
      lastRetryTime: Date.now()
    });
  };

  handleReset = () => {
    this.setState({
      hasError: false,
      error: undefined,
      errorInfo: undefined,
      retryCount: 0,
      isRetrying: false,
      lastRetryTime: 0,
      canAutoRetry: true
    });
  };

  render() {
    if (this.state.hasError) {
      const { fallback, gracefulDegradation, maxRetries = 3 } = this.props;
      const { retryCount, isRetrying, canAutoRetry } = this.state;

      // Use custom fallback if provided
      if (fallback) {
        return fallback;
      }

      // Use graceful degradation if max retries exceeded
      if (retryCount >= maxRetries && gracefulDegradation) {
        return (
          <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800">
            <div className="flex items-center mb-2">
              <Shield className="h-5 w-5 text-yellow-500 mr-2" />
              <h3 className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                Graceful Degradation Mode
              </h3>
            </div>
            <p className="text-xs text-yellow-700 dark:text-yellow-300 mb-3">
              The component encountered repeated errors. Showing simplified version.
            </p>
            {gracefulDegradation}
          </div>
        );
      }

      // Default enhanced error UI
      return (
        <div className="min-h-[400px] flex items-center justify-center bg-gray-50 dark:bg-gray-900 px-4">
          <div className="max-w-md w-full bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 text-center">
            <div className="flex justify-center mb-4">
              <AlertTriangle className="h-12 w-12 text-red-500" />
            </div>
            
            <h1 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2">
              Component Error
            </h1>
            
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              This component encountered an error and couldn't render properly.
            </p>

            {/* Retry Information */}
            {retryCount > 0 && (
              <div className="mb-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <p className="text-sm text-blue-700 dark:text-blue-300">
                  Retry attempts: {retryCount}/{maxRetries}
                </p>
                {!canAutoRetry && (
                  <p className="text-xs text-blue-600 dark:text-blue-400 mt-1">
                    Auto-retry disabled after multiple failures
                  </p>
                )}
              </div>
            )}

            {/* Loading state during retry */}
            {isRetrying && (
              <div className="mb-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                <div className="flex items-center justify-center">
                  <Clock className="h-4 w-4 text-yellow-500 mr-2 animate-spin" />
                  <p className="text-sm text-yellow-700 dark:text-yellow-300">
                    Retrying automatically...
                  </p>
                </div>
              </div>
            )}

            {/* Development error details */}
            {process.env.NODE_ENV === 'development' && this.state.error && (
              <div className="mb-6 p-4 bg-red-50 dark:bg-red-900/20 rounded-lg text-left">
                <h3 className="text-sm font-medium text-red-800 dark:text-red-200 mb-2">
                  Error Details (Development Only):
                </h3>
                <pre className="text-xs text-red-700 dark:text-red-300 overflow-auto">
                  {this.state.error instanceof Error ? error instanceof Error ? error.message : String(error) : String(error)}
                </pre>
                {this.state.error instanceof Error ? error.stack : String(error) && (
                  <details className="mt-2">
                    <summary className="text-xs text-red-600 dark:text-red-400 cursor-pointer">
                      Stack Trace
                    </summary>
                    <pre className="text-xs text-red-700 dark:text-red-300 mt-1 overflow-auto">
                      {this.state.error instanceof Error ? error.stack : String(error)}
                    </pre>
                  </details>
                )}
              </div>
            )}

            {/* Action buttons */}
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              {retryCount < maxRetries && (
                <button
                  onClick={() => this.handleRetry(false)}
                  disabled={isRetrying}
                  className="inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
                >
                  <RefreshCw className={`h-4 w-4 mr-2 ${isRetrying ? 'animate-spin' : ''}`} />
                  {isRetrying ? 'Retrying...' : 'Try Again'}
                </button>
              )}
              
              <button
                onClick={this.handleReset}
                className="inline-flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
              >
                Reset Component
              </button>
              
              <Link
                href="/"
                className="inline-flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
              >
                <Home className="h-4 w-4 mr-2" />
                Go Home
              </Link>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

/**
 * Hook for enhanced error handling in functional components
 */
export function useEnhancedErrorHandler(context = 'component') {

  const handleError = React.useCallback((error: Error, errorInfo?: ErrorInfo) => {
    console.error('Enhanced error handler:', error, errorInfo);
    
    errorTracker.captureException(error, {
      tags: { 
        context,
        errorHandler: 'enhanced',
        component: 'hook'
      },
      extra: errorInfo
    });
  }, [context]);

  const recoverFromError = React.useCallback(async (
    operation: () => Promise<any>,
    fallback?: any
  ): Promise<any> => {
    try {
      const result = await ErrorRecoveryManager.withRecovery(operation, {
        maxRetries: 2,
        context,
        fallbackValue: fallback
      });

      return result.data;
    } catch (error) {
      handleError(error instanceof Error ? error : new Error(String(error)));
      return fallback;
    }
  }, [context, handleError]);

  return {
    handleError,
    recoverFromError
  };
}

export default EnhancedErrorBoundary;
