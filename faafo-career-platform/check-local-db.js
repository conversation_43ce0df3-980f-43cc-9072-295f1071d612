require('dotenv').config();
const { PrismaClient } = require('@prisma/client');
const fs = require('fs');
const path = require('path');

async function checkLocalDatabases() {
  console.log('🔍 Checking for local database files...');
  
  // Check if local SQLite files exist
  const devDbPath = path.join(__dirname, 'prisma', 'dev.db');
  const devSqlitePath = path.join(__dirname, 'prisma', 'dev.sqlite');
  
  console.log(`📁 dev.db exists: ${fs.existsSync(devDbPath)}`);
  console.log(`📁 dev.sqlite exists: ${fs.existsSync(devSqlitePath)}`);
  
  if (fs.existsSync(devDbPath)) {
    console.log(`📊 dev.db size: ${fs.statSync(devDbPath).size} bytes`);
  }
  
  if (fs.existsSync(devSqlitePath)) {
    console.log(`📊 dev.sqlite size: ${fs.statSync(devSqlitePath).size} bytes`);
  }
  
  // Check what DATABASE_URL is actually being used
  console.log('\n🔗 Environment check:');
  console.log(`DATABASE_URL: ${process.env.DATABASE_URL ? 'Set' : 'Not set'}`);
  console.log(`NODE_ENV: ${process.env.NODE_ENV || 'undefined'}`);
  
  // Try connecting with different database URLs
  console.log('\n🧪 Testing database connections...');
  
  // Test 1: Current environment DATABASE_URL
  try {
    console.log('\n1️⃣ Testing current DATABASE_URL...');
    const prisma1 = new PrismaClient();
    const users1 = await prisma1.user.findMany();
    console.log(`✅ Current DB - Found ${users1.length} users`);
    if (users1.length > 0) {
      console.log(`   First user: ${users1[0].email} (ID: ${users1[0].id})`);
    }
    await prisma1.$disconnect();
  } catch (error) {
    console.log(`❌ Current DB error: ${error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : String(error) : String(error) : String(error) : String(error)}`);
  }
  
  // Test 2: Local SQLite dev.db
  if (fs.existsSync(devDbPath)) {
    try {
      console.log('\n2️⃣ Testing local dev.db...');
      const prisma2 = new PrismaClient({
        datasources: {
          db: {
            url: `file:${devDbPath}`
          }
        }
      });
      const users2 = await prisma2.user.findMany();
      console.log(`✅ Local dev.db - Found ${users2.length} users`);
      if (users2.length > 0) {
        console.log(`   First user: ${users2[0].email} (ID: ${users2[0].id})`);
      }
      await prisma2.$disconnect();
    } catch (error) {
      console.log(`❌ Local dev.db error: ${error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : String(error) : String(error) : String(error) : String(error)}`);
    }
  }
  
  // Test 3: Local SQLite dev.sqlite
  if (fs.existsSync(devSqlitePath)) {
    try {
      console.log('\n3️⃣ Testing local dev.sqlite...');
      const prisma3 = new PrismaClient({
        datasources: {
          db: {
            url: `file:${devSqlitePath}`
          }
        }
      });
      const users3 = await prisma3.user.findMany();
      console.log(`✅ Local dev.sqlite - Found ${users3.length} users`);
      if (users3.length > 0) {
        console.log(`   First user: ${users3[0].email} (ID: ${users3[0].id})`);
      }
      await prisma3.$disconnect();
    } catch (error) {
      console.log(`❌ Local dev.sqlite error: ${error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : String(error) : String(error) : String(error) : String(error)}`);
    }
  }
}

checkLocalDatabases()
  .catch((error) => {
    console.error('❌ Error checking databases:', error);
    process.exit(1);
  });
