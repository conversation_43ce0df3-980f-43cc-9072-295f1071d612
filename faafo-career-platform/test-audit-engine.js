#!/usr/bin/env node

/**
 * Simple Audit Engine Test
 * Tests the core audit engine functionality without complex dependencies
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 Audit Engine Functionality Test');
console.log('=' .repeat(50));

let testsPassed = 0;
let testsFailed = 0;

function logTest(testName, passed, details = '') {
  const status = passed ? '✅' : '❌';
  console.log(`${status} ${testName}${details ? ` - ${details}` : ''}`);
  
  if (passed) {
    testsPassed++;
  } else {
    testsFailed++;
  }
}

// Test 1: Check if audit engine can be imported
console.log('\n📦 Testing Module Imports...');

try {
  // Check if the audit service exists and has the right structure
  const auditServicePath = path.join(process.cwd(), 'src/lib/audit/audit-service.ts');
  const auditServiceContent = fs.readFileSync(auditServicePath, 'utf8');
  
  const hasClass = auditServiceContent.includes('class AuditService');
  const hasStartAudit = auditServiceContent.includes('startAudit');
  const hasGetIssues = auditServiceContent.includes('getIssues');
  const hasGetAuditRuns = auditServiceContent.includes('getAuditRuns');
  
  logTest('AuditService class exists', hasClass);
  logTest('AuditService has startAudit method', hasStartAudit);
  logTest('AuditService has getIssues method', hasGetIssues);
  logTest('AuditService has getAuditRuns method', hasGetAuditRuns);
  
} catch (error) {
  logTest('AuditService module check', false, error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : String(error) : String(error) : String(error) : String(error) : String(error) : String(error) : String(error) : String(error) : String(error));
}

// Test 2: Check analyzer structure
console.log('\n🔍 Testing Analyzer Structure...');

const analyzers = [
  'typescript-analyzer.ts',
  'eslint-analyzer.ts', 
  'security-analyzer.ts',
  'performance-analyzer.ts'
];

for (const analyzer of analyzers) {
  try {
    const analyzerPath = path.join(process.cwd(), 'src/lib/audit/analyzers', analyzer);
    const analyzerContent = fs.readFileSync(analyzerPath, 'utf8');
    
    const hasClass = analyzerContent.includes('class') || analyzerContent.includes('export');
    const hasAnalyze = analyzerContent.includes('analyze');
    
    logTest(`${analyzer} structure`, hasClass && hasAnalyze, 
      `Has class: ${hasClass}, Has analyze: ${hasAnalyze}`);
    
  } catch (error) {
    logTest(`${analyzer} structure`, false, 'File not found');
  }
}

// Test 3: Check types and interfaces
console.log('\n📝 Testing Type Definitions...');

try {
  const typesPath = path.join(process.cwd(), 'src/lib/audit/types.ts');
  const typesContent = fs.readFileSync(typesPath, 'utf8');
  
  const hasIssueSeverity = typesContent.includes('IssueSeverity');
  const hasIssueCategory = typesContent.includes('IssueCategory');
  const hasIssueStatus = typesContent.includes('IssueStatus');
  const hasAuditEngine = typesContent.includes('AuditEngine');
  const hasAuditRunResult = typesContent.includes('AuditRunResult');
  
  logTest('IssueSeverity enum', hasIssueSeverity);
  logTest('IssueCategory enum', hasIssueCategory);
  logTest('IssueStatus enum', hasIssueStatus);
  logTest('AuditEngine interface', hasAuditEngine);
  logTest('AuditRunResult interface', hasAuditRunResult);
  
} catch (error) {
  logTest('Type definitions check', false, error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : String(error) : String(error) : String(error) : String(error) : String(error) : String(error) : String(error) : String(error) : String(error));
}

// Test 4: Check storage implementation
console.log('\n💾 Testing Storage Implementation...');

try {
  const storagePath = path.join(process.cwd(), 'src/lib/audit/storage/audit-storage.ts');
  const storageContent = fs.readFileSync(storagePath, 'utf8');
  
  const hasClass = storageContent.includes('class AuditStorage');
  const hasSaveRun = storageContent.includes('saveAuditRun');
  const hasSaveIssue = storageContent.includes('saveIssue');
  const hasGetRuns = storageContent.includes('getAuditRuns');
  const hasGetIssues = storageContent.includes('getIssues');
  
  logTest('AuditStorage class', hasClass);
  logTest('Save audit run method', hasSaveRun);
  logTest('Save issue method', hasSaveIssue);
  logTest('Get audit runs method', hasGetRuns);
  logTest('Get issues method', hasGetIssues);
  
} catch (error) {
  logTest('Storage implementation check', false, error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : String(error) : String(error) : String(error) : String(error) : String(error) : String(error) : String(error) : String(error) : String(error));
}

// Test 5: Check core engine implementation
console.log('\n⚙️  Testing Core Engine Implementation...');

try {
  const enginePath = path.join(process.cwd(), 'src/lib/audit/core-audit-engine.ts');
  const engineContent = fs.readFileSync(enginePath, 'utf8');
  
  const hasClass = engineContent.includes('class CoreAuditEngine');
  const implementsInterface = engineContent.includes('implements AuditEngine');
  const hasRunAudit = engineContent.includes('runAudit');
  const hasAnalyzers = engineContent.includes('analyzeTypeScript') &&
                      engineContent.includes('analyzeSecurity') &&
                      engineContent.includes('analyzePerformance');
  const hasProgressCallback = engineContent.includes('progressCallback');
  
  logTest('CoreAuditEngine class', hasClass);
  logTest('Implements AuditEngine interface', implementsInterface);
  logTest('Has runAudit method', hasRunAudit);
  logTest('Has analyzer methods', hasAnalyzers);
  logTest('Has progress callback support', hasProgressCallback);
  
} catch (error) {
  logTest('Core engine implementation check', false, error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : String(error) : String(error) : String(error) : String(error) : String(error) : String(error) : String(error) : String(error) : String(error));
}

// Test 6: Check CLI implementation
console.log('\n💻 Testing CLI Implementation...');

try {
  const cliPath = path.join(process.cwd(), 'src/lib/audit/cli/audit-cli.ts');
  const cliContent = fs.readFileSync(cliPath, 'utf8');
  
  const hasRunCommand = cliContent.includes('run') || cliContent.includes('audit');
  const hasProgressDisplay = cliContent.includes('progress') || cliContent.includes('spinner');
  const hasResultsDisplay = cliContent.includes('results') || cliContent.includes('summary');
  
  logTest('CLI run command', hasRunCommand);
  logTest('CLI progress display', hasProgressDisplay);
  logTest('CLI results display', hasResultsDisplay);
  
} catch (error) {
  logTest('CLI implementation check', false, 'CLI file not found');
}

// Test 7: Check index exports
console.log('\n📤 Testing Module Exports...');

try {
  const indexPath = path.join(process.cwd(), 'src/lib/audit/index.ts');
  const indexContent = fs.readFileSync(indexPath, 'utf8');
  
  const exportsAuditService = indexContent.includes('AuditService');
  const exportsEngine = indexContent.includes('CoreAuditEngine');
  const exportsTypes = indexContent.includes('types');
  const exportsAnalyzers = indexContent.includes('Analyzer');
  
  logTest('Exports AuditService', exportsAuditService);
  logTest('Exports CoreAuditEngine', exportsEngine);
  logTest('Exports types', exportsTypes);
  logTest('Exports analyzers', exportsAnalyzers);
  
} catch (error) {
  logTest('Module exports check', false, error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : String(error) : String(error) : String(error) : String(error) : String(error) : String(error) : String(error) : String(error) : String(error));
}

// Test 8: Validate configuration structure
console.log('\n⚙️  Testing Configuration...');

try {
  const enginePath = path.join(process.cwd(), 'src/lib/audit/core-audit-engine.ts');
  const engineContent = fs.readFileSync(enginePath, 'utf8');
  
  const hasConfig = engineContent.includes('AuditRunConfig') || engineContent.includes('config');
  const hasContext = engineContent.includes('AnalysisContext') || engineContent.includes('context');
  const hasExcludePatterns = engineContent.includes('excludePatterns') || engineContent.includes('exclude');
  const hasIncludePatterns = engineContent.includes('includePatterns') || engineContent.includes('include');
  
  logTest('Has configuration support', hasConfig);
  logTest('Has analysis context', hasContext);
  logTest('Has exclude patterns', hasExcludePatterns);
  logTest('Has include patterns', hasIncludePatterns);
  
} catch (error) {
  logTest('Configuration structure check', false, error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : String(error) : String(error) : String(error) : String(error) : String(error) : String(error) : String(error) : String(error) : String(error));
}

// Summary
console.log('\n' + '=' .repeat(50));
console.log('📊 Engine Test Results:');
console.log(`✅ Passed: ${testsPassed}`);
console.log(`❌ Failed: ${testsFailed}`);

if (testsFailed === 0) {
  console.log('\n🎉 All engine tests passed!');
  console.log('✨ Audit engine is properly implemented.');
} else {
  console.log(`\n⚠️  ${testsFailed} tests failed.`);
  console.log('🔧 Review the implementation issues above.');
}

console.log('\n🔧 Engine Testing Complete!');
console.log('=' .repeat(50));
