require('dotenv').config();
const { PrismaClient } = require('@prisma/client');

async function checkRuntimeDatabase() {
  console.log('🔍 Checking what database the app is actually using...');
  
  // Check environment variables
  console.log('\n📋 Environment Variables:');
  console.log(`NODE_ENV: ${process.env.NODE_ENV || 'undefined'}`);
  console.log(`DATABASE_URL: ${process.env.DATABASE_URL ? 'Set' : 'Not set'}`);
  
  if (process.env.DATABASE_URL) {
    const dbUrl = process.env.DATABASE_URL;
    if (dbUrl.includes('neon.tech')) {
      console.log('🔗 Database Type: Neon PostgreSQL (Production)');
    } else if (dbUrl.includes('file:')) {
      console.log('🔗 Database Type: SQLite (Local)');
      console.log(`🔗 SQLite File: ${dbUrl}`);
    } else if (dbUrl.includes('postgres://') || dbUrl.includes('postgresql://')) {
      console.log('🔗 Database Type: PostgreSQL');
    } else {
      console.log(`🔗 Database Type: Unknown - ${dbUrl.substring(0, 20)}...`);
    }
  }
  
  // Test actual database connection
  console.log('\n🧪 Testing actual database connection...');
  
  try {
    const prisma = new PrismaClient();
    
    // Get database info
    const users = await prisma.user.findMany({
      select: {
        id: true,
        email: true,
        name: true,
        createdAt: true,
        emailVerified: true
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 5
    });
    
    console.log(`✅ Database Connection: SUCCESS`);
    console.log(`📊 Total Users: ${users.length}`);
    
    if (users.length > 0) {
      console.log('\n👥 Recent Users:');
      users.forEach((user, index) => {
        console.log(`   ${index + 1}. ${user.email} (ID: ${user.id.substring(0, 8)}...)`);
        console.log(`      Created: ${user.createdAt}`);
        console.log(`      Verified: ${user.emailVerified ? 'Yes' : 'No'}`);
      });
    }
    
    // Check for the specific user causing the error
    const testUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' },
      include: {
        assessments: {
          orderBy: { createdAt: 'desc' },
          take: 3
        }
      }
    });

    if (testUser) {
      console.log('\n🎯 Test User (<EMAIL>):');
      console.log(`   ID: ${testUser.id}`);
      console.log(`   Name: ${testUser.name}`);
      console.log(`   Email Verified: ${testUser.emailVerified}`);
      console.log(`   Assessments: ${testUser.assessments.length}`);

      if (testUser.assessments.length > 0) {
        console.log('\n📝 Recent Assessments for Test User:');
        testUser.assessments.forEach((assessment, index) => {
          console.log(`   ${index + 1}. ID: ${assessment.id.substring(0, 8)}... Status: ${assessment.status}`);
          console.log(`      Step: ${assessment.currentStep}/6 Created: ${assessment.createdAt}`);
          console.log(`      Updated: ${assessment.updatedAt}`);
        });
      }
    } else {
      console.log('\n❌ Test User (<EMAIL>) NOT FOUND in this database');
    }
    
    await prisma.$disconnect();
    
  } catch (error) {
    console.log(`❌ Database Connection Error: ${error instanceof Error ? error instanceof Error ? error.message : String(error) : String(error)}`);
  }
}

checkRuntimeDatabase()
  .catch((error) => {
    console.error('❌ Error:', error);
    process.exit(1);
  });
