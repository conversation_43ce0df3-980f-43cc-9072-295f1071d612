#!/usr/bin/env node

const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));

const BASE_URL = 'http://localhost:3000';

async function testEndpoint(endpoint, method = 'GET', body = null, headers = {}) {
  try {
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      }
    };
    
    if (body) {
      options.body = JSON.stringify(body);
    }

    console.log(`\n🔍 Testing ${method} ${endpoint}`);
    const response = await fetch(`${BASE_URL}${endpoint}`, options);
    
    console.log(`📡 Status: ${response.status} ${response.statusText}`);
    
    if (response.headers.get('content-type')?.includes('application/json')) {
      const data = await response.json();
      console.log(`📊 Response:`, JSON.stringify(data, null, 2));
    } else {
      const text = await response.text();
      console.log(`📄 Response:`, text.substring(0, 200) + (text.length > 200 ? '...' : ''));
    }
    
    return { status: response.status, ok: response.ok };
  } catch (error) {
    console.error(`❌ Error testing ${endpoint}:`, error instanceof Error ? error instanceof Error ? error.message : String(error) : String(error));
    return { status: 0, ok: false, error: error instanceof Error ? error instanceof Error ? error.message : String(error) : String(error) };
  }
}

async function runTests() {
  console.log('🚀 Starting API endpoint tests...\n');

  // Test endpoints that should work without authentication
  await testEndpoint('/api/health');
  
  // Test endpoints that require authentication (should return 401)
  await testEndpoint('/api/assessment?status=true');
  await testEndpoint('/api/freedom-fund');
  await testEndpoint('/api/ai/skills-analysis/comprehensive', 'POST', {
    currentSkills: [],
    targetCareerPath: {
      careerPathName: 'Full Stack Developer',
      targetLevel: 'INTERMEDIATE'
    },
    preferences: {
      timeframe: 'SIX_MONTHS',
      hoursPerWeek: 10,
      learningStyle: [],
      budget: 'ANY',
      focusAreas: []
    },
    includeMarketData: true,
    includePersonalizedPaths: true
  });

  console.log('\n✅ API endpoint tests completed');
}

runTests().catch(console.error);
